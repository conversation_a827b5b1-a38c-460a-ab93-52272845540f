"""
配置相关的数据模型
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
from enum import Enum
from pathlib import Path


class DownloadMode(Enum):
    """下载模式枚举"""
    DATE = "date"        # 按日期 - 从关注画师新作品页面收集
    RANKING = "ranking"  # 排行榜模式
    SEARCH = "search"    # 搜索模式
    USER = "user"        # 用户作品模式
    BOOKMARK = "bookmark"  # 收藏模式
    FOLLOW = "follow"    # 关注模式


class ClassifyMode(Enum):
    """分类模式枚举"""
    BY_DATE = "by_date"
    BY_AUTHOR = "by_author"
    BY_TYPE = "by_type"
    FLAT = "flat"


class GifMode(Enum):
    """GIF模式枚举"""
    GIF_ONLY = "gif_only"
    FRAMES_ONLY = "frames_only"
    BOTH = "both"


class DateMode(Enum):
    """关注画师模式子模式枚举"""
    BY_PAGE_RANGE = "by_page_range"  # 按页码范围下载
    BY_DATE_RANGE = "by_date_range"  # 按日期范围下载（带页码限制）


class SearchCategory(Enum):
    """搜索种类枚举"""
    GENERAL = "综合"      # artworks
    ILLUSTRATION = "插画"  # illustrations
    MANGA = "漫画"        # manga
    NOVEL = "小说"        # novels


class SearchBookmarkCount(Enum):
    """搜索收藏值枚举"""
    DISABLED = -1         # 不启用收藏过滤
    COUNT_1000 = 1000     # 1000users入り
    COUNT_2000 = 2000     # 2000users入り
    COUNT_5000 = 5000     # 5000users入り
    COUNT_10000 = 10000   # 10000users入り
    COUNT_20000 = 20000   # 20000users入り


class SearchContentMode(Enum):
    """搜索内容模式枚举"""
    ALL = "全部"        # 全部内容
    SAFE = "全年龄"      # 全年龄内容 (?mode=safe)
    R18 = "R18"         # R18内容 (?mode=r18)


class SearchMode(Enum):
    """搜索模式子模式枚举"""
    BY_PAGE_RANGE = "by_page_range"  # 按页码范围下载
    BY_KEYWORD = "by_keyword"        # 按关键词搜索（原有逻辑）


class RankingCategory(Enum):
    """排行榜一级分类"""
    GENERAL = "综合"      # 无content参数
    ILLUSTRATION = "插画"  # content=illust
    UGOIRA = "动图"       # content=ugoira
    MANGA = "漫画"        # content=manga


class RankingRating(Enum):
    """排行榜二级分类（评级）"""
    GENERAL = "一般"      # (默认)
    R18 = "R-18"         # _r18


class RankingPeriod(Enum):
    """排行榜时间周期"""
    DAILY = "今日"        # daily
    WEEKLY = "本周"       # weekly
    AI = "AI生成"         # ai (仅综合分类可用)


@dataclass
class SearchConfig:
    """搜索配置"""
    category: SearchCategory = SearchCategory.GENERAL
    bookmark_count: SearchBookmarkCount = SearchBookmarkCount.DISABLED
    content_mode: SearchContentMode = SearchContentMode.ALL

    def get_search_url(self, keyword: str, page: int = 1) -> str:
        """
        构建搜索URL

        Args:
            keyword: 搜索关键词（需要为英文或日文）
            page: 页码

        Returns:
            str: 完整的搜索URL
        """
        import urllib.parse

        # URL编码搜索关键词
        encoded_keyword = urllib.parse.quote(keyword)

        # 构建种类参数
        category_map = {
            SearchCategory.GENERAL: "artworks",
            SearchCategory.ILLUSTRATION: "illustrations",
            SearchCategory.MANGA: "manga",
            SearchCategory.NOVEL: "novels"
        }
        category_param = category_map[self.category]

        # 构建基础关键词部分
        if self.bookmark_count == SearchBookmarkCount.DISABLED:
            # 不启用收藏过滤
            keyword_part = encoded_keyword
        else:
            # 启用收藏过滤 - 正确格式：关键词+收藏数+users入り（无空格）
            bookmark_value = self.bookmark_count.value
            keyword_part = f"{encoded_keyword}{bookmark_value}users入り"

        # 构建内容模式部分
        if self.content_mode == SearchContentMode.SAFE:
            mode_part = "safe"
        elif self.content_mode == SearchContentMode.R18:
            mode_part = "r18"
        else:
            # 全部模式不添加mode
            mode_part = ""

        # 构建完整URL路径
        if mode_part:
            base_url = f"https://www.pixiv.net/tags/{keyword_part}/{category_param}?mode={mode_part}"
        else:
            base_url = f"https://www.pixiv.net/tags/{keyword_part}/{category_param}"

        # 构建URL参数
        params = []

        # 添加页码参数
        if page > 1:
            params.append(f"p={page}")

        # 构建完整URL
        if params:
            return f"{base_url}?{'&'.join(params)}"
        else:
            return base_url

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'category': self.category.value,
            'bookmark_count': self.bookmark_count.value,
            'content_mode': self.content_mode.value
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'SearchConfig':
        """从字典创建配置"""
        return cls(
            category=SearchCategory(data.get('category', '综合')),
            bookmark_count=SearchBookmarkCount(data.get('bookmark_count', -1)),
            content_mode=SearchContentMode(data.get('content_mode', '全部'))
        )


@dataclass
class RankingConfig:
    """排行榜下载配置"""
    category: RankingCategory = RankingCategory.ILLUSTRATION
    rating: RankingRating = RankingRating.GENERAL
    period: RankingPeriod = RankingPeriod.DAILY
    specific_date: Optional[str] = None  # 格式: "2025-07-25"
    custom_save_path: Optional[str] = None  # 排行榜专用保存路径
    auto_folder_naming: bool = True  # 是否自动按分类命名文件夹
    
    def get_ranking_url(self, page: int = 1, date: Optional[str] = None) -> str:
        """
        构建排行榜URL

        Args:
            page: 页码
            date: 日期 (格式: YYYYMMDD)

        Returns:
            str: 完整的排行榜URL
        """
        # 构建mode参数 - 修复AI生成模式，_ai永远加在末尾
        if self.period == RankingPeriod.AI:
            # AI生成模式：先构建基础mode，再添加_ai后缀
            if self.rating == RankingRating.R18:
                mode = "daily_r18_ai"  # R18 AI生成
            else:
                mode = "daily_ai"      # 普通AI生成
        else:
            # 非AI模式：构建基础mode
            period_map = {
                RankingPeriod.DAILY: "daily",
                RankingPeriod.WEEKLY: "weekly"
            }
            base_mode = period_map.get(self.period, "daily")

            # 添加R18后缀
            mode = f"{base_mode}_r18" if self.rating == RankingRating.R18 else base_mode

        # 构建URL参数列表
        params = [f"mode={mode}"]

        # 添加content参数 - 使用字典映射优化
        content_map = {
            RankingCategory.ILLUSTRATION: "illust",
            RankingCategory.UGOIRA: "ugoira",
            RankingCategory.MANGA: "manga"
        }

        if self.category in content_map:
            params.append(f"content={content_map[self.category]}")
        # 综合分类(GENERAL)不添加content参数

        # 添加页码参数（只有大于1时才添加）
        if page > 1:
            params.append(f"p={page}")

        # 添加日期参数
        if date:
            params.append(f"date={date}")

        # 构建完整URL
        return f"https://www.pixiv.net/ranking.php?{'&'.join(params)}"

    def get_pixiv_mode(self) -> str:
        """获取Pixiv API的排行榜模式参数 (保持向后兼容)"""
        # 构建mode参数 - 与get_ranking_url保持一致
        if self.period == RankingPeriod.AI:
            # AI生成模式：_ai永远加在末尾
            if self.rating == RankingRating.R18:
                mode = "daily_r18_ai"  # R18 AI生成
            else:
                mode = "daily_ai"      # 普通AI生成
        else:
            # 非AI模式
            if self.period == RankingPeriod.DAILY:
                mode = "daily"
            elif self.period == RankingPeriod.WEEKLY:
                mode = "weekly"
            else:
                mode = "daily"

            # 添加R18后缀
            if self.rating == RankingRating.R18:
                mode += "_r18"

        return mode

    def validate_config(self) -> List[str]:
        """
        验证排行榜配置

        Returns:
            List[str]: 错误信息列表，空列表表示验证通过
        """
        errors = []

        # AI生成只能在综合分类下使用
        if self.period == RankingPeriod.AI and self.category != RankingCategory.GENERAL:
            errors.append("AI生成分类只能在综合排行榜下使用")

        # 验证日期格式
        if self.specific_date:
            if len(self.specific_date) != 8 or not self.specific_date.isdigit():
                errors.append("日期格式应为8位数字 (YYYYMMDD)")

        return errors

    def get_available_periods(self) -> List[RankingPeriod]:
        """
        获取当前分类下可用的时间周期选项

        Returns:
            List[RankingPeriod]: 可用的时间周期列表
        """
        if self.category == RankingCategory.GENERAL:
            # 综合分类支持所有选项
            return list(RankingPeriod)
        else:
            # 其他分类不支持AI生成
            return [period for period in RankingPeriod if period != RankingPeriod.AI]

    def get_folder_name(self) -> str:
        """获取文件夹命名"""
        if not self.auto_folder_naming:
            return "ranking"
        
        parts = [
            self.category.value,
            self.rating.value,
            self.period.value
        ]
        
        if self.specific_date:
            parts.append(self.specific_date)
        
        return "_".join(parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'category': self.category.name,
            'rating': self.rating.name,
            'period': self.period.name,
            'specific_date': self.specific_date,
            'custom_save_path': self.custom_save_path,
            'auto_folder_naming': self.auto_folder_naming
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RankingConfig':
        """从字典创建实例"""
        category = RankingCategory.ILLUSTRATION
        for cat in RankingCategory:
            if cat.name == data.get('category', 'ILLUSTRATION'):
                category = cat
                break
                
        rating = RankingRating.GENERAL
        for rat in RankingRating:
            if rat.name == data.get('rating', 'GENERAL'):
                rating = rat
                break
                
        period = RankingPeriod.DAILY
        for per in RankingPeriod:
            if per.name == data.get('period', 'DAILY'):
                period = per
                break
        
        return cls(
            category=category,
            rating=rating,
            period=period,
            specific_date=data.get('specific_date'),
            custom_save_path=data.get('custom_save_path'),
            auto_folder_naming=data.get('auto_folder_naming', True)
        )


@dataclass
class DownloadConfig:
    """下载配置数据模型"""
    # 基本设置
    save_path: str = "pixiv_imgs"  # 画师新作保存路径
    search_save_path: str = ""     # 搜索结果保存路径
    user_save_path: str = ""       # 用户作品保存路径
    download_mode: DownloadMode = DownloadMode.DATE
    gif_mode: GifMode = GifMode.GIF_ONLY
    classify_mode: ClassifyMode = ClassifyMode.BY_DATE

    # 页面设置
    start_page: int = 1
    end_page: int = 5
    days: int = 1

    # 关注画师模式设置
    date_mode: DateMode = DateMode.BY_DATE_RANGE  # 关注画师子模式

    # 排行榜设置
    ranking_config: RankingConfig = field(default_factory=RankingConfig)

    # 搜索设置
    search_keyword: str = ""
    search_order: str = "date_desc"
    search_mode: str = "all"
    search_config: SearchConfig = field(default_factory=SearchConfig)
    search_sub_mode: SearchMode = SearchMode.BY_PAGE_RANGE  # 搜索子模式（固定为页码范围）

    # 用户设置
    user_id: Optional[int] = None

    # 过滤设置
    min_bookmarks: int = 0
    max_bookmarks: int = 0
    min_pages: int = 0
    max_pages: int = 0
    exclude_tags: List[str] = field(default_factory=list)
    include_tags: List[str] = field(default_factory=list)

    # 下载控制
    skip_existing: bool = True
    create_info_file: bool = True
    download_limit: int = 0

    def get_user_artworks_url(self, page: int = 1) -> str:
        """
        构建画师作品URL

        Args:
            page: 页码

        Returns:
            str: 完整的画师作品URL

        Raises:
            ValueError: 当user_id未设置时抛出异常
        """
        if not self.user_id:
            raise ValueError("画师作品模式下必须设置user_id")

        # 构建基础URL
        base_url = f"https://www.pixiv.net/users/{self.user_id}/artworks"

        # 添加页码参数（只有大于1时才添加）
        if page > 1:
            return f"{base_url}?p={page}"
        else:
            return base_url
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'save_path': self.save_path,
            'search_save_path': self.search_save_path,
            'user_save_path': self.user_save_path,
            'download_mode': self.download_mode.value,
            'gif_mode': self.gif_mode.value,
            'classify_mode': self.classify_mode.value,
            'start_page': self.start_page,
            'end_page': self.end_page,
            'days': self.days,
            'date_mode': self.date_mode.value,
            'search_keyword': self.search_keyword,
            'search_order': self.search_order,
            'search_mode': self.search_mode,
            'search_config': self.search_config.to_dict(),
            'search_sub_mode': self.search_sub_mode.value,
            'user_id': self.user_id,
            'min_bookmarks': self.min_bookmarks,
            'max_bookmarks': self.max_bookmarks,
            'min_pages': self.min_pages,
            'max_pages': self.max_pages,
            'exclude_tags': self.exclude_tags,
            'include_tags': self.include_tags,
            'skip_existing': self.skip_existing,
            'create_info_file': self.create_info_file,
            'download_limit': self.download_limit,
            'ranking_config': self.ranking_config.to_dict()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DownloadConfig':
        """从字典创建实例"""
        return cls(
            save_path=data.get('save_path', 'pixiv_imgs'),
            search_save_path=data.get('search_save_path', ''),
            user_save_path=data.get('user_save_path', ''),
            download_mode=DownloadMode(data.get('download_mode', 'date')),
            gif_mode=GifMode(data.get('gif_mode', 'gif_only')),
            classify_mode=ClassifyMode(data.get('classify_mode', 'by_date')),
            start_page=data.get('start_page', 1),
            end_page=data.get('end_page', 5),
            days=data.get('days', 1),
            date_mode=DateMode(data.get('date_mode', 'by_date_range')),
            search_keyword=data.get('search_keyword', ''),
            search_order=data.get('search_order', 'date_desc'),
            search_mode=data.get('search_mode', 'all'),
            search_config=SearchConfig.from_dict(data.get('search_config', {})),
            search_sub_mode=SearchMode(data.get('search_sub_mode', 'by_page_range')),
            user_id=data.get('user_id'),
            min_bookmarks=data.get('min_bookmarks', 0),
            max_bookmarks=data.get('max_bookmarks', 0),
            min_pages=data.get('min_pages', 0),
            max_pages=data.get('max_pages', 0),
            exclude_tags=data.get('exclude_tags', []),
            include_tags=data.get('include_tags', []),
            skip_existing=data.get('skip_existing', True),
            create_info_file=data.get('create_info_file', True),
            download_limit=data.get('download_limit', 0),
            ranking_config=RankingConfig.from_dict(data.get('ranking_config', {}))
        )
    
    def get_save_directory(self) -> Path:
        """获取保存目录路径"""
        return Path(self.save_path)
    
    def validate(self) -> List[str]:
        """验证配置，返回错误信息列表 - 使用统一验证器"""
        # 使用统一验证器进行验证，避免重复的验证逻辑
        from ..utils.unified_validator import unified_validator
        return unified_validator.validate_download_config(self)


@dataclass
class SpiderConfig:
    """爬虫全局配置"""
    # 性能设置
    max_workers: int = 8
    request_timeout: int = 30
    retry_attempts: int = 3
    retry_delay: float = 1.0
    concurrent_downloads: int = 8  # 增加并发下载数
    
    # 缓存设置
    api_cache_size: int = 1000
    cache_expire_time: int = 3600
    
    # 网络设置
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    referer: str = "https://www.pixiv.net/"
    proxy: Optional[str] = None
    
    # Selenium设置
    selenium_headless: bool = True
    selenium_timeout: int = 10
    selenium_page_load_timeout: int = 30
    
    # 日志设置
    log_level: str = "INFO"
    log_file: Optional[str] = None
    log_max_size: int = 10485760  # 10MB
    log_backup_count: int = 5
    
    # 其他设置
    cookies_file: str = "pixiv_cookies.pkl"
    config_file: str = "pixiv_config.json"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'max_workers': self.max_workers,
            'request_timeout': self.request_timeout,
            'retry_attempts': self.retry_attempts,
            'retry_delay': self.retry_delay,
            'concurrent_downloads': self.concurrent_downloads,
            'api_cache_size': self.api_cache_size,
            'cache_expire_time': self.cache_expire_time,
            'user_agent': self.user_agent,
            'referer': self.referer,
            'proxy': self.proxy,
            'selenium_headless': self.selenium_headless,
            'selenium_timeout': self.selenium_timeout,
            'selenium_page_load_timeout': self.selenium_page_load_timeout,
            'log_level': self.log_level,
            'log_file': self.log_file,
            'log_max_size': self.log_max_size,
            'log_backup_count': self.log_backup_count,
            'cookies_file': self.cookies_file,
            'config_file': self.config_file
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SpiderConfig':
        """从字典创建实例"""
        return cls(
            max_workers=data.get('max_workers', 8),
            request_timeout=data.get('request_timeout', 30),
            retry_attempts=data.get('retry_attempts', 3),
            retry_delay=data.get('retry_delay', 1.0),
            concurrent_downloads=data.get('concurrent_downloads', 4),
            api_cache_size=data.get('api_cache_size', 1000),
            cache_expire_time=data.get('cache_expire_time', 3600),
            user_agent=data.get('user_agent', "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"),
            referer=data.get('referer', "https://www.pixiv.net/"),
            proxy=data.get('proxy'),
            selenium_headless=data.get('selenium_headless', True),
            selenium_timeout=data.get('selenium_timeout', 10),
            selenium_page_load_timeout=data.get('selenium_page_load_timeout', 30),
            log_level=data.get('log_level', 'INFO'),
            log_file=data.get('log_file'),
            log_max_size=data.get('log_max_size', 10485760),
            log_backup_count=data.get('log_backup_count', 5),
            cookies_file=data.get('cookies_file', 'pixiv_cookies.pkl'),
            config_file=data.get('config_file', 'pixiv_config.json')
        ) 