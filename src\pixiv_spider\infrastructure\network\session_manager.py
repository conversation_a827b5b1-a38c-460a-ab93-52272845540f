"""
会话管理器

专门负责HTTP会话的创建、管理和池化
"""

import queue
import requests
import logging
from typing import List, Dict, Any, Optional
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from ...models.config import SpiderConfig


class SessionManager:
    """HTTP会话管理器 - 专注会话池管理"""
    
    def __init__(self, cookies: List[Dict[str, Any]], spider_config: SpiderConfig):
        """
        初始化会话管理器
        
        Args:
            cookies: 认证cookies
            spider_config: 爬虫配置
        """
        self.cookies = cookies
        self.spider_config = spider_config
        self.logger = logging.getLogger(__name__)
        
        # 会话池
        self.session_pool = queue.Queue()
        self.max_workers = spider_config.max_workers
        
        # 请求头模板
        self.headers = {
            'Referer': spider_config.referer,
            'User-Agent': spider_config.user_agent,
            'Accept': 'application/json',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache'
        }
        
        # 初始化会话池
        self._initialize_session_pool()
    
    def _initialize_session_pool(self) -> None:
        """初始化会话池"""
        for _ in range(self.max_workers):
            session = self._create_session()
            self.session_pool.put(session)
        
        self.logger.info(f"🔧 初始化会话池，大小: {self.max_workers}")
    
    def _create_session(self) -> requests.Session:
        """
        创建单个会话
        
        Returns:
            requests.Session: 配置好的会话对象
        """
        session = requests.Session()
        
        # 设置cookies
        if self.cookies:
            for cookie in self.cookies:
                session.cookies.set(
                    cookie['name'], 
                    cookie['value'], 
                    domain=cookie.get('domain')
                )
        
        # 设置请求头
        session.headers.update(self.headers)
        
        # 设置重试策略
        retry_strategy = Retry(
            total=self.spider_config.retry_attempts,
            backoff_factor=self.spider_config.retry_delay,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]  # 新版本urllib3的参数名
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置代理
        if self.spider_config.proxy:
            session.proxies = {
                'http': self.spider_config.proxy,
                'https': self.spider_config.proxy
            }
        
        return session
    
    def get_session(self) -> requests.Session:
        """
        获取会话
        
        Returns:
            requests.Session: 可用的会话对象
        """
        try:
            return self.session_pool.get_nowait()
        except queue.Empty:
            # 如果池为空，创建新会话
            self.logger.debug("会话池为空，创建新会话")
            return self._create_session()
    
    def return_session(self, session: requests.Session) -> None:
        """
        归还会话到池中
        
        Args:
            session: 要归还的会话对象
        """
        try:
            self.session_pool.put_nowait(session)
        except queue.Full:
            # 如果池已满，关闭会话
            self.logger.debug("会话池已满，关闭会话")
            session.close()
    
    def update_cookies(self, new_cookies: List[Dict[str, Any]]) -> None:
        """
        更新cookies并重新初始化会话池
        
        Args:
            new_cookies: 新的cookies列表
        """
        self.logger.info("🍪 更新cookies并重新初始化会话池")
        
        # 清理现有会话池
        self.cleanup_sessions()
        
        # 更新cookies
        self.cookies = new_cookies
        
        # 重新初始化会话池
        self._initialize_session_pool()
    
    def update_headers(self, new_headers: Dict[str, str]) -> None:
        """
        更新请求头模板
        
        Args:
            new_headers: 新的请求头
        """
        self.headers.update(new_headers)
        self.logger.debug(f"📝 更新请求头: {new_headers}")
    
    def get_pool_status(self) -> Dict[str, Any]:
        """
        获取会话池状态
        
        Returns:
            Dict[str, Any]: 会话池状态信息
        """
        return {
            'pool_size': self.session_pool.qsize(),
            'max_workers': self.max_workers,
            'cookies_count': len(self.cookies) if self.cookies else 0,
            'has_proxy': bool(self.spider_config.proxy)
        }
    
    def test_session(self, test_url: str = "https://www.pixiv.net") -> bool:
        """
        测试会话连接
        
        Args:
            test_url: 测试URL
            
        Returns:
            bool: 连接是否成功
        """
        session = self.get_session()
        try:
            response = session.head(test_url, timeout=10)
            success = response.status_code in [200, 301, 302, 403]  # 403也算正常，可能是反爬虫
            
            if success:
                self.logger.debug(f"✅ 会话测试成功: {response.status_code}")
            else:
                self.logger.warning(f"⚠️ 会话测试失败: {response.status_code}")
            
            return success
        
        except Exception as e:
            self.logger.warning(f"⚠️ 会话测试异常: {e}")
            return False
        finally:
            self.return_session(session)
    
    def cleanup_sessions(self) -> None:
        """清理所有会话"""
        try:
            self.logger.info("🧹 开始清理会话池...")
            closed_count = 0
            
            # 清理会话池中的所有会话
            while not self.session_pool.empty():
                try:
                    session = self.session_pool.get_nowait()
                    # 确保会话完全关闭
                    if hasattr(session, 'close'):
                        session.close()
                    # 清理会话的适配器
                    if hasattr(session, 'adapters'):
                        for adapter in session.adapters.values():
                            if hasattr(adapter, 'close'):
                                adapter.close()
                    closed_count += 1
                except queue.Empty:
                    break
                except Exception as e:
                    self.logger.debug(f"关闭会话失败: {e}")
            
            # 强制垃圾回收
            import gc
            gc.collect()
            
            self.logger.info(f"✅ 会话池已清理，关闭了 {closed_count} 个会话")
        
        except Exception as e:
            self.logger.error(f"❌ 清理会话池失败: {e}")
    
    def recreate_pool(self) -> None:
        """重新创建会话池"""
        self.logger.info("🔄 重新创建会话池...")
        
        # 清理现有会话
        self.cleanup_sessions()
        
        # 重新初始化
        self._initialize_session_pool()
        
        self.logger.info("✅ 会话池重新创建完成")
    
    def __del__(self):
        """析构函数，清理会话池"""
        try:
            self.cleanup_sessions()
        except Exception:
            # 析构函数中不应该抛出异常
            pass
