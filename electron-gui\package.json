{"name": "pixiv-spider-gui", "version": "1.0.0", "description": "Modern Electron + Vue GUI for Pixiv Spider", "main": "src/main/index.js", "scripts": {"dev": "concurrently \"npm run dev:vue\" \"npm run dev:electron\"", "dev:vue": "vite", "dev:electron": "wait-on http://localhost:5173 && electron .", "start": "electron .", "start-fresh": "npm run build:vue && electron .", "app": "cross-env NODE_ENV=production electron .", "app-fresh": "npm run build:vue && cross-env NODE_ENV=production electron .", "build": "npm run build:vue && npm run build:electron", "build:vue": "vite build", "build:electron": "electron-builder", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"axios": "^1.6.0", "electron-store": "^8.1.0", "socket.io-client": "^4.7.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.4.0", "concurrently": "^8.2.0", "cross-env": "^10.0.0", "electron": "^27.0.0", "electron-builder": "^24.13.3", "element-plus": "^2.4.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "terser": "^5.43.1", "unplugin-auto-import": "^0.16.0", "unplugin-vue-components": "^0.25.0", "vite": "^4.4.5", "vue": "^3.3.4", "vue-router": "^4.2.0", "vuex": "^4.1.0", "wait-on": "^7.0.1"}, "build": {"appId": "com.pixivspider.gui", "productName": "Pixiv Spider", "directories": {"output": "dist"}, "files": ["src/main/**/*", "dist-vue/**/*", "node_modules/**/*"], "extraResources": [{"from": "../src", "to": "python-backend", "filter": ["**/*"]}]}}