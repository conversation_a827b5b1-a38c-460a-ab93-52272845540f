"""
认证管理器

专门负责处理用户认证和Cookie管理
"""

import logging
from typing import Optional, Dict, Any, Tuple

from .spider_context import SpiderContext
from ..models.exceptions import AuthenticationError


class AuthenticationManager:
    """认证管理器 - 专注认证和Cookie管理"""
    
    def __init__(self, context: SpiderContext):
        """
        初始化认证管理器
        
        Args:
            context: 爬虫上下文
        """
        self.context = context
        self.logger = logging.getLogger(__name__)
        
        # 认证状态
        self._is_authenticated = False
        self._cookies: Optional[Dict[str, Any]] = None
    
    @property
    def is_authenticated(self) -> bool:
        """是否已认证"""
        return self._is_authenticated
    
    @property
    def cookies(self) -> Optional[Dict[str, Any]]:
        """获取当前cookies"""
        return self._cookies
    
    def authenticate(self) -> bool:
        """
        进行身份验证
        
        Returns:
            bool: 是否验证成功
        """
        try:
            # 确保服务已初始化
            self.context.ensure_services_initialized()
            
            # 尝试加载已保存的Cookie
            success, cookies = self.context.auth_service.check_login_status()
            
            if success and cookies:
                self._cookies = cookies
                self.context.setup_services_with_cookies(cookies)
                self._is_authenticated = True
                self.logger.info("使用已保存的Cookie登录成功")
                return True
            
            # 如果没有有效Cookie，需要重新登录
            self.logger.warning("未找到有效的登录信息，需要重新登录")
            return False
            
        except Exception as e:
            self.logger.error(f"身份验证失败: {e}")
            return False
    
    def interactive_login(self) -> bool:
        """
        交互式登录
        
        Returns:
            bool: 是否登录成功
        """
        try:
            # 确保服务已初始化
            self.context.ensure_services_initialized()
            
            success, cookies = self.context.auth_service.get_or_create_cookies()
            
            if success and cookies:
                self._cookies = cookies
                self.context.setup_services_with_cookies(cookies)
                self._is_authenticated = True
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"交互式登录失败: {e}")
            return False
    
    def logout(self) -> None:
        """登出"""
        self._is_authenticated = False
        self._cookies = None
        self.logger.info("已登出")
    
    def refresh_authentication(self) -> bool:
        """
        刷新认证状态
        
        Returns:
            bool: 刷新是否成功
        """
        if not self._cookies:
            return False
        
        try:
            # 验证当前cookies是否仍然有效
            success, _ = self.context.auth_service.check_login_status()
            if not success:
                self.logger.warning("当前认证已失效")
                self._is_authenticated = False
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"刷新认证状态失败: {e}")
            self._is_authenticated = False
            return False
    
    def get_authentication_status(self) -> Dict[str, Any]:
        """
        获取认证状态信息
        
        Returns:
            Dict[str, Any]: 认证状态信息
        """
        return {
            'is_authenticated': self._is_authenticated,
            'has_cookies': self._cookies is not None,
            'cookies_count': len(self._cookies) if self._cookies else 0
        }
