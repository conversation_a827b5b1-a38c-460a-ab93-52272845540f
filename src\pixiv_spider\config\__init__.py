"""
配置模块 - 重构后的统一配置系统

提供统一的配置管理、验证、工厂等功能
"""

# 核心配置管理
from .config_manager import ConfigManager

# 配置工厂和验证
from .config_factory import ConfigFactory
from .config_validator import ConfigValidator

# 配置常量和模板
from .config_constants import (
    Paths, ApiConstants, NetworkDefaults, PerformanceDefaults,
    SeleniumDefaults, LogDefaults, GuiDefaults, DownloadDefaults,
    FileTypes, ConfigTemplates, ValidationRules, Environment,
    Compatibility
)

# 向后兼容的设置导入
from . import settings

__all__ = [
    # 核心类
    "ConfigManager",
    "ConfigFactory",
    "ConfigValidator",

    # 常量类
    "Paths",
    "ApiConstants",
    "NetworkDefaults",
    "PerformanceDefaults",
    "SeleniumDefaults",
    "LogDefaults",
    "GuiDefaults",
    "DownloadDefaults",
    "FileTypes",
    "ConfigTemplates",
    "ValidationRules",
    "Environment",
    "Compatibility",

    # 向后兼容
    "settings",
]