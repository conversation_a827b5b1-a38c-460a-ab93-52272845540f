"""
作品处理器模块

负责作品数据的解析、处理和过滤
"""

import logging
import re
import time
from datetime import datetime
from typing import List, Optional, Dict, Any, Set
from concurrent.futures import ThreadPoolExecutor, as_completed

from ..models.artwork import Artwork, ArtworkType, ArtworkStatus
from ..models.config import DownloadConfig
from ..interfaces.api_interface import IApiService
from ..utils.cache_manager import CacheManager


class ArtworkProcessor:
    """作品处理器"""

    def __init__(self, api_service: IApiService, download_config: DownloadConfig, 
                 cache_manager: CacheManager):
        """
        初始化作品处理器
        
        Args:
            api_service: API服务接口
            download_config: 下载配置
            cache_manager: 缓存管理器
        """
        self.logger = logging.getLogger(__name__)
        self.api_service = api_service
        self.download_config = download_config
        self.cache_manager = cache_manager

    def extract_artwork_id(self, link_or_data) -> Optional[int]:
        """
        从链接或数据中提取作品ID

        Args:
            link_or_data: 作品链接字符串或包含ID的数据字典

        Returns:
            Optional[int]: 作品ID，提取失败时返回None
        """
        try:
            if isinstance(link_or_data, str):
                # 从链接中提取ID
                match = re.search(r'/artworks/(\d+)', link_or_data)
                if match:
                    return int(match.group(1))
            elif isinstance(link_or_data, dict):
                # 从数据字典中提取ID
                if 'id' in link_or_data:
                    return int(link_or_data['id'])
                elif 'illust_id' in link_or_data:
                    return int(link_or_data['illust_id'])
            return None
        except (ValueError, AttributeError):
            return None

    def create_artwork_from_detail_data(self, data: Dict[str, Any]) -> Artwork:
        """从详情数据创建作品对象"""
        # 解析上传日期
        upload_date = None
        if data.get('uploadDate'):
            try:
                upload_date = datetime.fromisoformat(data['uploadDate'].replace('Z', '+00:00'))
            except Exception:
                pass
        
        # 确定作品类型
        illust_type = data.get('illustType', 0)
        if illust_type == 0:
            artwork_type = ArtworkType.ILLUSTRATION
        elif illust_type == 1:
            artwork_type = ArtworkType.MANGA
        elif illust_type == 2:
            artwork_type = ArtworkType.UGOIRA
        else:
            artwork_type = ArtworkType.ILLUSTRATION
        
        # 创建作品对象
        artwork = Artwork(
            id=int(data['id']),
            title=data.get('title', 'Unknown'),
            author_id=int(data.get('userId', 0)),
            author_name=data.get('userName', 'Unknown'),
            type=artwork_type,
            tags=[tag['tag'] for tag in data.get('tags', {}).get('tags', [])],
            description=data.get('description', ''),
            view_count=data.get('viewCount', 0),
            like_count=data.get('likeCount', 0),
            bookmark_count=data.get('bookmarkCount', 0),
            upload_date=upload_date
        )
        
        return artwork

    def create_artwork_from_ranking_data(self, data: Dict[str, Any]) -> Optional[Artwork]:
        """从排行榜数据创建作品对象"""
        try:
            artwork_id = int(data['illust_id'])
            detail_data = self.api_service.get_artwork_detail(artwork_id)
            
            if detail_data and 'body' in detail_data:
                return self.create_artwork_from_detail_data(detail_data['body'])
            
            return None
            
        except Exception as e:
            self.logger.error(f"从排行榜数据创建作品对象失败: {e}")
            return None

    def create_artwork_from_search_data(self, data: Dict[str, Any]) -> Optional[Artwork]:
        """从搜索数据创建作品对象"""
        try:
            artwork_id = int(data['id'])
            detail_data = self.api_service.get_artwork_detail(artwork_id)
            
            if detail_data and 'body' in detail_data:
                return self.create_artwork_from_detail_data(detail_data['body'])
            
            return None
            
        except Exception as e:
            self.logger.error(f"从搜索数据创建作品对象失败: {e}")
            return None

    def process_artwork_link(self, link: str) -> Optional[Artwork]:
        """处理作品链接，获取作品信息"""
        try:
            # 从链接中提取作品ID
            artwork_id = self.extract_artwork_id(link)
            if not artwork_id:
                return None

            # 检查缓存
            cached_artwork = self.cache_manager.get_artwork(artwork_id)
            if cached_artwork is not None:
                return cached_artwork

            # 获取作品详情
            detail_data = self.api_service.get_artwork_detail(artwork_id)
            if not detail_data or 'body' not in detail_data:
                # 缓存失败结果
                self.cache_manager.put_artwork(artwork_id, None)
                return None

            artwork_info = detail_data['body']
            
            # 创建作品对象
            artwork = self.create_artwork_from_detail_data(artwork_info)
            
            # 应用过滤条件
            filter_reason = self.get_filter_reason(artwork)
            if filter_reason:
                self.logger.debug(f"⏭️ 作品 {artwork.id} - {artwork.title} 被过滤: {filter_reason}")
                # 缓存过滤结果
                self.cache_manager.put_artwork(artwork_id, None)
                return None

            # 缓存成功结果
            self.cache_manager.put_artwork(artwork_id, artwork)
            return artwork
            
        except Exception as e:
            self.logger.error(f"❌ 处理作品链接失败: {link}, 错误: {e}")
            # 缓存失败结果
            if 'artwork_id' in locals():
                self.cache_manager.put_artwork(artwork_id, None)
            return None

    def get_filter_reason(self, artwork: Artwork) -> str:
        """获取作品被过滤的原因，如果不应该过滤则返回空字符串"""
        # 收藏数过滤
        if self.download_config.min_bookmarks > 0 and artwork.bookmark_count < self.download_config.min_bookmarks:
            return f"收藏数过低 ({artwork.bookmark_count} < {self.download_config.min_bookmarks})"

        if self.download_config.max_bookmarks > 0 and artwork.bookmark_count > self.download_config.max_bookmarks:
            return f"收藏数过高 ({artwork.bookmark_count} > {self.download_config.max_bookmarks})"

        # 页数过滤
        if self.download_config.min_pages > 0 and artwork.page_count < self.download_config.min_pages:
            return f"页数过少 ({artwork.page_count} < {self.download_config.min_pages})"

        if self.download_config.max_pages > 0 and artwork.page_count > self.download_config.max_pages:
            return f"页数过多 ({artwork.page_count} > {self.download_config.max_pages})"

        # 标签过滤
        if self.download_config.exclude_tags:
            for exclude_tag in self.download_config.exclude_tags:
                if any(exclude_tag.lower() in tag.lower() for tag in artwork.tags):
                    return f"包含排除标签: {exclude_tag}"

        if self.download_config.include_tags:
            has_include_tag = False
            for include_tag in self.download_config.include_tags:
                if any(include_tag.lower() in tag.lower() for tag in artwork.tags):
                    has_include_tag = True
                    break
            if not has_include_tag:
                return f"不包含必需标签: {', '.join(self.download_config.include_tags)}"

        return ""

    def should_filter_artwork(self, artwork: Artwork) -> bool:
        """检查是否应该过滤作品"""
        return bool(self.get_filter_reason(artwork))

    def process_artwork_links_parallel(self, artwork_links: Set[str], max_workers: int = None) -> List[Artwork]:
        """
        并行处理作品链接（优化版本）

        Args:
            artwork_links: 作品链接集合
            max_workers: 最大并发数

        Returns:
            List[Artwork]: 处理成功的作品列表
        """
        if not artwork_links:
            return []

        # 使用配置中的并发数，如果没有指定则使用默认值
        if max_workers is None:
            max_workers = getattr(self.download_config, 'max_workers', 8)

        self.logger.info(f"⚙️ 开始优化并行处理 {len(artwork_links)} 个作品链接，并发数: {max_workers}")

        # 1. 提取所有作品ID
        self.logger.info("🔍 提取作品ID...")
        artwork_ids = []
        link_to_id = {}

        for link in artwork_links:
            artwork_id = self.extract_artwork_id(link)
            if artwork_id:
                # 检查缓存
                if not self.cache_manager.is_artwork_cached(artwork_id):
                    artwork_ids.append(artwork_id)
                link_to_id[link] = artwork_id

        cached_count = len(artwork_links) - len(artwork_ids)
        if cached_count > 0:
            self.logger.info(f"📦 从缓存中获取 {cached_count} 个作品")

        # 2. 批量获取作品详情（未缓存的）
        if artwork_ids:
            self.logger.info(f"🌐 批量获取 {len(artwork_ids)} 个作品详情")
            # 分批处理，避免单次请求过多
            batch_size = min(50, max_workers * 5)  # 每批最多50个，或并发数的5倍
            details_batch = {}

            batch_count = (len(artwork_ids) + batch_size - 1) // batch_size
            for i in range(0, len(artwork_ids), batch_size):
                batch_num = i // batch_size + 1
                batch_ids = artwork_ids[i:i + batch_size]
                self.logger.info(f"📥 处理第 {batch_num}/{batch_count} 批，共 {len(batch_ids)} 个作品")

                batch_results = self.api_service.get_artwork_details_batch(batch_ids, max_workers)
                details_batch.update(batch_results)

                # 批次间短暂延迟，避免过于频繁的请求
                if i + batch_size < len(artwork_ids):
                    time.sleep(0.2)

            # 3. 批量处理和缓存结果
            self.logger.info(f"🔧 开始处理和缓存 {len(details_batch)} 个作品详情...")
            processed_count = 0
            filtered_count = 0

            for artwork_id, detail_data in details_batch.items():
                processed_count += 1
                if detail_data and 'body' in detail_data:
                    try:
                        artwork = self.create_artwork_from_detail_data(detail_data['body'])
                        filter_reason = self.get_filter_reason(artwork)
                        if not filter_reason:
                            self.cache_manager.put_artwork(artwork_id, artwork)
                        else:
                            filtered_count += 1
                            self.logger.debug(f"⏭️ 作品 {artwork.id} 被过滤: {filter_reason}")
                            self.cache_manager.put_artwork(artwork_id, None)
                    except Exception as e:
                        self.logger.error(f"❌ 创建作品对象失败: {artwork_id}, 错误: {e}")
                        self.cache_manager.put_artwork(artwork_id, None)
                else:
                    self.cache_manager.put_artwork(artwork_id, None)

            if filtered_count > 0:
                self.logger.info(f"🔍 过滤了 {filtered_count} 个不符合条件的作品")

        # 4. 收集所有成功的作品
        self.logger.info("📋 收集处理结果...")
        artworks = []
        for link in artwork_links:
            artwork_id = link_to_id.get(link)
            if artwork_id:
                artwork = self.cache_manager.get_artwork(artwork_id)
                if artwork:
                    artworks.append(artwork)

        success_rate = (len(artworks) / len(artwork_links)) * 100 if artwork_links else 0
        self.logger.info(f"✅ 优化并行处理完成，成功处理 {len(artworks)}/{len(artwork_links)} 个作品 ({success_rate:.1f}%)")
        return artworks
