"""
内存监控器

提供内存使用监控和泄漏检测功能
"""

import gc
import logging
import psutil
import threading
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass


@dataclass
class MemorySnapshot:
    """内存快照"""
    timestamp: float
    rss_mb: float  # 物理内存使用量(MB)
    vms_mb: float  # 虚拟内存使用量(MB)
    percent: float  # 内存使用百分比
    gc_objects: int  # GC对象数量
    thread_count: int  # 线程数量


class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, check_interval: float = 30.0, max_snapshots: int = 100):
        """
        初始化内存监控器
        
        Args:
            check_interval: 检查间隔(秒)
            max_snapshots: 最大快照数量
        """
        self.logger = logging.getLogger(__name__)
        self.check_interval = check_interval
        self.max_snapshots = max_snapshots
        
        # 内存快照历史
        self.snapshots: List[MemorySnapshot] = []
        
        # 监控线程
        self._monitor_thread: Optional[threading.Thread] = None
        self._is_monitoring = False
        
        # 内存泄漏阈值
        self.memory_leak_threshold_mb = 500  # 内存增长超过500MB认为可能泄漏
        self.gc_threshold = 10000  # GC对象数量阈值
        
    def start_monitoring(self) -> None:
        """开始内存监控"""
        if self._is_monitoring:
            self.logger.warning("内存监控已在运行")
            return
            
        self._is_monitoring = True
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        self.logger.info(f"🔍 内存监控已启动，检查间隔: {self.check_interval}秒")
        
    def stop_monitoring(self) -> None:
        """停止内存监控"""
        if not self._is_monitoring:
            return
            
        self._is_monitoring = False
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=5)
        self.logger.info("⏹️ 内存监控已停止")
        
    def _monitor_loop(self) -> None:
        """监控循环"""
        while self._is_monitoring:
            try:
                snapshot = self._take_snapshot()
                self._add_snapshot(snapshot)
                self._check_memory_leak()
                time.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f"内存监控异常: {e}")
                time.sleep(self.check_interval)
                
    def _take_snapshot(self) -> MemorySnapshot:
        """获取内存快照"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return MemorySnapshot(
            timestamp=time.time(),
            rss_mb=memory_info.rss / 1024 / 1024,
            vms_mb=memory_info.vms / 1024 / 1024,
            percent=process.memory_percent(),
            gc_objects=len(gc.get_objects()),
            thread_count=threading.active_count()
        )
        
    def _add_snapshot(self, snapshot: MemorySnapshot) -> None:
        """添加快照"""
        self.snapshots.append(snapshot)
        
        # 保持快照数量在限制内
        if len(self.snapshots) > self.max_snapshots:
            self.snapshots.pop(0)
            
    def _check_memory_leak(self) -> None:
        """检查内存泄漏"""
        if len(self.snapshots) < 5:  # 需要至少5个快照才能判断
            return
            
        # 检查内存增长趋势
        recent_snapshots = self.snapshots[-5:]
        first_snapshot = recent_snapshots[0]
        last_snapshot = recent_snapshots[-1]
        
        memory_growth = last_snapshot.rss_mb - first_snapshot.rss_mb
        gc_growth = last_snapshot.gc_objects - first_snapshot.gc_objects
        
        # 内存泄漏警告
        if memory_growth > self.memory_leak_threshold_mb:
            self.logger.warning(f"⚠️ 检测到可能的内存泄漏:")
            self.logger.warning(f"   📈 内存增长: {memory_growth:.1f}MB")
            self.logger.warning(f"   🧮 GC对象增长: {gc_growth}")
            self.logger.warning(f"   🧵 当前线程数: {last_snapshot.thread_count}")
            
            # 触发垃圾回收
            self.force_garbage_collection()
            
    def force_garbage_collection(self) -> Dict[str, int]:
        """强制垃圾回收"""
        self.logger.info("🗑️ 执行强制垃圾回收...")
        
        # 记录回收前状态
        before_objects = len(gc.get_objects())
        
        # 执行垃圾回收
        collected = gc.collect()
        
        # 记录回收后状态
        after_objects = len(gc.get_objects())
        freed_objects = before_objects - after_objects
        
        result = {
            'collected': collected,
            'freed_objects': freed_objects,
            'remaining_objects': after_objects
        }
        
        self.logger.info(f"✅ 垃圾回收完成: 回收{collected}个循环引用, 释放{freed_objects}个对象")
        return result
        
    def get_memory_stats(self) -> Dict[str, Any]:
        """获取内存统计信息"""
        if not self.snapshots:
            return {}
            
        latest = self.snapshots[-1]
        
        # 计算内存使用趋势
        if len(self.snapshots) >= 2:
            previous = self.snapshots[-2]
            memory_trend = latest.rss_mb - previous.rss_mb
            gc_trend = latest.gc_objects - previous.gc_objects
        else:
            memory_trend = 0
            gc_trend = 0
            
        return {
            'current_memory_mb': latest.rss_mb,
            'current_memory_percent': latest.percent,
            'memory_trend_mb': memory_trend,
            'gc_objects': latest.gc_objects,
            'gc_trend': gc_trend,
            'thread_count': latest.thread_count,
            'snapshots_count': len(self.snapshots)
        }
        
    def cleanup_resources(self) -> None:
        """清理监控器资源"""
        try:
            self.stop_monitoring()
            self.snapshots.clear()
            self.logger.info("✅ 内存监控器资源已清理")
        except Exception as e:
            self.logger.error(f"❌ 清理内存监控器资源失败: {e}")
            
    def __del__(self):
        """析构函数"""
        try:
            self.cleanup_resources()
        except Exception:
            pass
