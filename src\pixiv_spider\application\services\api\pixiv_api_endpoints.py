"""
Pixiv API端点服务

专门负责Pixiv API的具体端点调用
"""

import logging
import concurrent.futures
from typing import Optional, Dict, Any, List, Callable

from ....config.settings import PIXIV_BASE_URL
from ....infrastructure.network.http_client import HttpClient
from .api_cache_manager import ApiCacheManager


class PixivApiEndpoints:
    """Pixiv API端点服务 - 专注具体API调用"""
    
    def __init__(self, http_client: HttpClient, cache_manager: ApiCacheManager):
        """
        初始化Pixiv API端点服务
        
        Args:
            http_client: HTTP客户端
            cache_manager: 缓存管理器
        """
        self.http_client = http_client
        self.cache_manager = cache_manager
        self.logger = logging.getLogger(__name__)
    
    def get_artwork_detail(self, artwork_id: int, 
                          stop_signal_callback: Optional[Callable] = None) -> Optional[Dict[str, Any]]:
        """
        获取作品详情
        
        Args:
            artwork_id: 作品ID
            stop_signal_callback: 停止信号回调函数
            
        Returns:
            Optional[Dict[str, Any]]: 作品详情数据
        """
        url = f"{PIXIV_BASE_URL}/ajax/illust/{artwork_id}"
        
        # 使用缓存
        return self.cache_manager.get_cached_response(
            url,
            lambda: self.http_client.make_json_request(url, stop_signal_callback=stop_signal_callback)
        )
    
    def get_artwork_pages(self, artwork_id: int, 
                         stop_signal_callback: Optional[Callable] = None) -> Optional[Dict[str, Any]]:
        """
        获取作品页面信息
        
        Args:
            artwork_id: 作品ID
            stop_signal_callback: 停止信号回调函数
            
        Returns:
            Optional[Dict[str, Any]]: 页面信息数据
        """
        # 检查停止信号
        if stop_signal_callback and stop_signal_callback():
            self.logger.debug(f"🛑 收到停止信号，取消获取页面信息: {artwork_id}")
            return None
        
        url = f"{PIXIV_BASE_URL}/ajax/illust/{artwork_id}/pages"
        
        # 使用缓存
        return self.cache_manager.get_cached_response(
            url,
            lambda: self.http_client.make_json_request(url, stop_signal_callback=stop_signal_callback)
        )
    
    def get_artwork_details_batch(self, artwork_ids: List[int], 
                                 max_workers: int = None,
                                 stop_signal_callback: Optional[Callable] = None) -> Dict[int, Optional[Dict[str, Any]]]:
        """
        批量获取作品详情
        
        Args:
            artwork_ids: 作品ID列表
            max_workers: 最大并发数
            stop_signal_callback: 停止信号回调函数
            
        Returns:
            Dict[int, Optional[Dict[str, Any]]]: 作品ID到详情数据的映射
        """
        if not artwork_ids:
            return {}
        
        # 检查停止信号
        if stop_signal_callback and stop_signal_callback():
            self.logger.debug("🛑 收到停止信号，取消批量获取作品详情")
            return {}
        
        if max_workers is None:
            max_workers = min(10, len(artwork_ids))  # 默认最大10个并发
        
        results = {}
        
        self.logger.info(f"📦 开始批量获取 {len(artwork_ids)} 个作品详情，并发数: {max_workers}")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_id = {}
            for artwork_id in artwork_ids:
                # 在提交前检查停止信号
                if stop_signal_callback and stop_signal_callback():
                    self.logger.debug("🛑 提交过程中收到停止信号")
                    break
                
                future = executor.submit(self.get_artwork_detail, artwork_id, stop_signal_callback)
                future_to_id[future] = artwork_id
            
            # 收集结果
            completed_count = 0
            for future in concurrent.futures.as_completed(future_to_id, timeout=None):
                # 检查停止信号
                if stop_signal_callback and stop_signal_callback():
                    self.logger.debug("🛑 收集结果过程中收到停止信号")
                    break
                
                artwork_id = future_to_id[future]
                try:
                    result = future.result(timeout=30)
                    results[artwork_id] = result
                    completed_count += 1
                    
                    # 定期输出进度
                    if completed_count % 10 == 0:
                        self.logger.debug(f"📊 批量获取进度: {completed_count}/{len(artwork_ids)}")
                
                except Exception as e:
                    self.logger.error(f"❌ 批量获取作品详情失败: {artwork_id}, 错误: {e}")
                    results[artwork_id] = None
        
        self.logger.info(f"✅ 批量获取完成: {len(results)}/{len(artwork_ids)} 个作品")
        return results
    
    def get_user_artworks(self, user_id: int, page: int = 1,
                         stop_signal_callback: Optional[Callable] = None) -> Optional[Dict[str, Any]]:
        """
        获取用户作品列表
        
        Args:
            user_id: 用户ID
            page: 页码
            stop_signal_callback: 停止信号回调函数
            
        Returns:
            Optional[Dict[str, Any]]: 用户作品数据
        """
        url = f"{PIXIV_BASE_URL}/ajax/user/{user_id}/profile/all"
        if page > 1:
            url += f"?page={page}"
        
        # 用户数据可能变化，使用较短的缓存时间或不缓存
        return self.http_client.make_json_request(url, stop_signal_callback=stop_signal_callback)
    
    def get_ranking_artworks(self, mode: str = "daily", date: str = None, page: int = 1,
                           stop_signal_callback: Optional[Callable] = None) -> Optional[Dict[str, Any]]:
        """
        获取排行榜作品
        
        Args:
            mode: 排行榜模式 (daily, weekly, monthly等)
            date: 日期 (YYYY-MM-DD格式)
            page: 页码
            stop_signal_callback: 停止信号回调函数
            
        Returns:
            Optional[Dict[str, Any]]: 排行榜数据
        """
        url = f"{PIXIV_BASE_URL}/ranking.php"
        params = {
            'mode': mode,
            'format': 'json',
            'page': page
        }
        if date:
            params['date'] = date
        
        # 排行榜数据实时性要求高，不使用缓存
        return self.http_client.make_json_request(
            url, 
            params=params, 
            stop_signal_callback=stop_signal_callback
        )
    
    def search_artworks(self, keyword: str, order: str = "date_desc", 
                       mode: str = "all", page: int = 1,
                       stop_signal_callback: Optional[Callable] = None) -> Optional[Dict[str, Any]]:
        """
        搜索作品
        
        Args:
            keyword: 搜索关键词
            order: 排序方式
            mode: 搜索模式
            page: 页码
            stop_signal_callback: 停止信号回调函数
            
        Returns:
            Optional[Dict[str, Any]]: 搜索结果数据
        """
        url = f"{PIXIV_BASE_URL}/ajax/search/artworks/{keyword}"
        params = {
            'word': keyword,
            'order': order,
            'mode': mode,
            'p': page,
            's_mode': 's_tag'
        }
        
        # 搜索结果可能变化，不使用缓存
        return self.http_client.make_json_request(
            url, 
            params=params, 
            stop_signal_callback=stop_signal_callback
        )
    
    def get_user_profile(self, user_id: int,
                        stop_signal_callback: Optional[Callable] = None) -> Optional[Dict[str, Any]]:
        """
        获取用户资料
        
        Args:
            user_id: 用户ID
            stop_signal_callback: 停止信号回调函数
            
        Returns:
            Optional[Dict[str, Any]]: 用户资料数据
        """
        url = f"{PIXIV_BASE_URL}/ajax/user/{user_id}"
        
        # 用户资料相对稳定，可以缓存
        return self.cache_manager.get_cached_response(
            url,
            lambda: self.http_client.make_json_request(url, stop_signal_callback=stop_signal_callback)
        )
    
    def get_artwork_ugoira_meta(self, artwork_id: int,
                               stop_signal_callback: Optional[Callable] = None) -> Optional[Dict[str, Any]]:
        """
        获取动图元数据
        
        Args:
            artwork_id: 作品ID
            stop_signal_callback: 停止信号回调函数
            
        Returns:
            Optional[Dict[str, Any]]: 动图元数据
        """
        url = f"{PIXIV_BASE_URL}/ajax/illust/{artwork_id}/ugoira_meta"
        
        # 动图元数据稳定，可以缓存
        return self.cache_manager.get_cached_response(
            url,
            lambda: self.http_client.make_json_request(url, stop_signal_callback=stop_signal_callback)
        )
    
    def get_related_artworks(self, artwork_id: int, limit: int = 18,
                           stop_signal_callback: Optional[Callable] = None) -> Optional[Dict[str, Any]]:
        """
        获取相关作品
        
        Args:
            artwork_id: 作品ID
            limit: 限制数量
            stop_signal_callback: 停止信号回调函数
            
        Returns:
            Optional[Dict[str, Any]]: 相关作品数据
        """
        url = f"{PIXIV_BASE_URL}/ajax/illust/{artwork_id}/recommend/init"
        params = {'limit': limit}
        
        # 相关作品可能变化，使用较短缓存或不缓存
        return self.http_client.make_json_request(
            url, 
            params=params, 
            stop_signal_callback=stop_signal_callback
        )
    
    def get_bookmark_data(self, artwork_id: int,
                         stop_signal_callback: Optional[Callable] = None) -> Optional[Dict[str, Any]]:
        """
        获取收藏数据
        
        Args:
            artwork_id: 作品ID
            stop_signal_callback: 停止信号回调函数
            
        Returns:
            Optional[Dict[str, Any]]: 收藏数据
        """
        url = f"{PIXIV_BASE_URL}/ajax/illust/{artwork_id}/bookmarks"
        
        # 收藏数据可能变化，不使用缓存
        return self.http_client.make_json_request(url, stop_signal_callback=stop_signal_callback)
    
    def test_api_connectivity(self) -> bool:
        """
        测试API连接性
        
        Returns:
            bool: API是否可访问
        """
        try:
            # 使用一个简单的API端点测试连接
            test_url = f"{PIXIV_BASE_URL}/ajax/top/illust"
            response = self.http_client.make_json_request(test_url)
            
            if response is not None:
                self.logger.info("✅ Pixiv API连接测试成功")
                return True
            else:
                self.logger.warning("⚠️ Pixiv API连接测试失败")
                return False
        
        except Exception as e:
            self.logger.error(f"❌ Pixiv API连接测试异常: {e}")
            return False
