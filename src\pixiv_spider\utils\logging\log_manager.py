"""
日志管理器

统一管理应用程序的日志配置和输出
"""

import logging
import logging.handlers
import os
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

from .log_formatter import LogFormatter


class LogManager:
    """日志管理器"""
    
    def __init__(self, log_dir: Optional[str] = None):
        """
        初始化日志管理器
        
        Args:
            log_dir: 日志目录路径
        """
        self.log_dir = Path(log_dir) if log_dir else Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        self.formatter = LogFormatter()
        self._loggers: Dict[str, logging.Logger] = {}
        
        # 设置根日志器
        self._setup_root_logger()
    
    def _setup_root_logger(self) -> None:
        """设置根日志器"""
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(self.formatter)
        root_logger.addHandler(console_handler)
        
        # 添加文件处理器
        log_file = self.log_dir / f"pixiv_spider_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
        )
        file_handler.setFormatter(self.formatter)
        root_logger.addHandler(file_handler)
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        获取指定名称的日志器
        
        Args:
            name: 日志器名称
            
        Returns:
            logging.Logger: 日志器实例
        """
        if name not in self._loggers:
            logger = logging.getLogger(name)
            self._loggers[name] = logger
        
        return self._loggers[name]
    
    def set_level(self, level: int) -> None:
        """
        设置日志级别
        
        Args:
            level: 日志级别
        """
        logging.getLogger().setLevel(level)
        for logger in self._loggers.values():
            logger.setLevel(level)
    
    def add_file_handler(self, name: str, filename: str) -> None:
        """
        为指定日志器添加文件处理器
        
        Args:
            name: 日志器名称
            filename: 文件名
        """
        logger = self.get_logger(name)
        
        log_file = self.log_dir / filename
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=5*1024*1024, backupCount=3, encoding='utf-8'
        )
        file_handler.setFormatter(self.formatter)
        logger.addHandler(file_handler)
    
    def cleanup(self) -> None:
        """清理日志管理器"""
        for logger in self._loggers.values():
            for handler in logger.handlers[:]:
                handler.close()
                logger.removeHandler(handler)
        
        self._loggers.clear()


# 全局日志管理器实例
_log_manager: Optional[LogManager] = None


def get_log_manager() -> LogManager:
    """获取全局日志管理器实例"""
    global _log_manager
    if _log_manager is None:
        _log_manager = LogManager()
    return _log_manager


def setup_logging(log_dir: Optional[str] = None, level: int = logging.INFO) -> LogManager:
    """
    设置应用程序日志
    
    Args:
        log_dir: 日志目录
        level: 日志级别
        
    Returns:
        LogManager: 日志管理器实例
    """
    global _log_manager
    _log_manager = LogManager(log_dir)
    _log_manager.set_level(level)
    return _log_manager


def get_logger(name: str) -> logging.Logger:
    """
    获取日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logging.Logger: 日志器实例
    """
    return get_log_manager().get_logger(name)
