"""
Selenium配置管理

统一管理Selenium相关的配置和选择器
"""

from typing import Dict, List, Any
from dataclasses import dataclass


@dataclass
class SeleniumConfig:
    """Selenium配置类"""
    
    # 超时设置
    page_load_timeout: int = 30
    implicit_wait: int = 10
    explicit_wait: int = 15
    
    # 浏览器设置
    headless: bool = True
    window_size: tuple = (1920, 1080)
    user_agent: str = ""
    
    # 性能设置
    disable_images: bool = True
    disable_css: bool = False
    disable_javascript: bool = False
    
    # 代理设置
    proxy: str = ""
    
    @classmethod
    def create_default(cls) -> 'SeleniumConfig':
        """创建默认配置"""
        return cls(
            page_load_timeout=30,
            implicit_wait=10,
            explicit_wait=15,
            headless=True,
            window_size=(1920, 1080),
            disable_images=True,
            disable_css=False,
            disable_javascript=False
        )
    
    @classmethod
    def create_fast_mode(cls) -> 'SeleniumConfig':
        """创建快速模式配置"""
        return cls(
            page_load_timeout=15,
            implicit_wait=5,
            explicit_wait=8,
            headless=True,
            window_size=(1366, 768),
            disable_images=True,
            disable_css=True,
            disable_javascript=False
        )


class SelectorConfig:
    """选择器配置类"""
    
    # 统一的选择器配置
    SELECTORS = {
        'artwork_links': [
            'a[href*="/artworks/"]',
            'a[href^="/artworks/"]',
            '.gtm-new-work-thumbnail-click',
            '[data-gtm-value*="artworks"]'
        ],
        'user_menu': [
            'a[data-gtm-value="header_user_menu"]',
            'img[alt*="avatar"]',
            '.user-icon',
            '[data-testid="header-user-menu"]',
            '.header-menu'
        ],
        'login_buttons': [
            'a[href*="login"]',
            'button[data-gtm-value="header_login"]',
            '.signup-form'
        ],
        'load_more': [
            'button[data-gtm-value="load_more"]',
            '.load-more-button',
            '[data-testid="load-more"]'
        ],
        'artwork_images': [
            'img[src*="i.pximg.net"]',
            'img[data-src*="i.pximg.net"]',
            '.artwork-image img'
        ]
    }
    
    @classmethod
    def get_selectors(cls, selector_type: str) -> List[str]:
        """获取指定类型的选择器列表"""
        return cls.SELECTORS.get(selector_type, [])
    
    @classmethod
    def get_combined_selector(cls, selector_type: str) -> str:
        """获取组合选择器字符串"""
        selectors = cls.get_selectors(selector_type)
        return ', '.join(selectors) if selectors else ""
    
    @classmethod
    def add_custom_selector(cls, selector_type: str, selector: str) -> None:
        """添加自定义选择器"""
        if selector_type not in cls.SELECTORS:
            cls.SELECTORS[selector_type] = []
        if selector not in cls.SELECTORS[selector_type]:
            cls.SELECTORS[selector_type].append(selector)


class ChromeOptionsBuilder:
    """Chrome选项构建器"""
    
    @staticmethod
    def build_options(config: SeleniumConfig) -> Any:
        """根据配置构建Chrome选项"""
        from selenium.webdriver.chrome.options import Options
        
        options = Options()
        
        # 基础设置
        if config.headless:
            options.add_argument('--headless')
        
        options.add_argument(f'--window-size={config.window_size[0]},{config.window_size[1]}')
        
        # 性能优化
        if config.disable_images:
            prefs = {"profile.managed_default_content_settings.images": 2}
            options.add_experimental_option("prefs", prefs)
        
        if config.disable_css:
            options.add_argument('--disable-web-security')
        
        # 用户代理
        if config.user_agent:
            options.add_argument(f'--user-agent={config.user_agent}')
        
        # 代理设置
        if config.proxy:
            options.add_argument(f'--proxy-server={config.proxy}')
        
        # 通用优化选项
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-renderer-backgrounding')
        
        # 隐藏自动化特征
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        return options
