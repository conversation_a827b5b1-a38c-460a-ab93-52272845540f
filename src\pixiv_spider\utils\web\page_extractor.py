"""
页面内容提取器

专门负责从网页中提取作品链接和其他内容
"""

import logging
import time
from typing import Set, List, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

from .selenium_config import SelectorConfig


class PageExtractor:
    """页面内容提取器"""
    
    def __init__(self, driver: webdriver.Chrome, wait_timeout: int = 15):
        """
        初始化页面提取器
        
        Args:
            driver: Chrome驱动器
            wait_timeout: 等待超时时间
        """
        self.driver = driver
        self.wait_timeout = wait_timeout
        self.logger = logging.getLogger(__name__)
        self.wait = WebDriverWait(driver, wait_timeout)
        
        # 缓存
        self._page_cache = {}
        self._cache_max_size = 100
    
    def extract_artwork_links(self, url: str, use_cache: bool = True) -> Set[str]:
        """
        提取页面中的作品链接
        
        Args:
            url: 页面URL
            use_cache: 是否使用缓存
            
        Returns:
            Set[str]: 作品链接集合
        """
        # 检查缓存
        if use_cache and url in self._page_cache:
            cached_links = self._page_cache[url].copy()
            self.logger.debug(f"💾 使用缓存的页面链接: {len(cached_links)} 个")
            return cached_links
        
        start_time = time.time()
        self.logger.info(f"🔍 开始提取页面链接: {url}")
        
        try:
            # 导航到页面
            self.driver.get(url)
            
            # 等待页面加载
            self._wait_for_page_load()
            
            # 提取链接
            artwork_links = self._extract_links_from_current_page()
            
            # 缓存结果
            if use_cache:
                self._cache_page_links(url, artwork_links)
            
            elapsed = time.time() - start_time
            self.logger.info(f"✅ 页面链接提取完成: {len(artwork_links)} 个链接 (耗时: {elapsed:.2f}秒)")
            
            return artwork_links
            
        except Exception as e:
            self.logger.error(f"❌ 提取页面链接失败: {url}, 错误: {e}")
            return set()
    
    def _wait_for_page_load(self) -> None:
        """等待页面加载完成"""
        try:
            # 等待基本元素加载
            self.wait.until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # 额外等待，确保动态内容加载
            time.sleep(1)
            
        except TimeoutException:
            self.logger.warning("⚠️ 页面加载超时，继续处理")
    
    def _extract_links_from_current_page(self) -> Set[str]:
        """从当前页面提取作品链接"""
        artwork_links = set()
        
        try:
            # 获取所有可能的作品链接选择器
            selectors = SelectorConfig.get_selectors('artwork_links')
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        try:
                            href = element.get_attribute('href')
                            if href and '/artworks/' in href:
                                # 标准化URL
                                if not href.startswith('https://'):
                                    href = 'https://www.pixiv.net' + href
                                artwork_links.add(href)
                        except Exception:
                            continue
                            
                except Exception as e:
                    self.logger.debug(f"选择器 {selector} 执行失败: {e}")
                    continue
            
            self.logger.debug(f"🔗 提取到 {len(artwork_links)} 个作品链接")
            
        except Exception as e:
            self.logger.error(f"❌ 提取链接时发生错误: {e}")
        
        return artwork_links
    
    def extract_with_scroll(self, url: str, max_scrolls: int = 5) -> Set[str]:
        """
        通过滚动页面提取更多链接
        
        Args:
            url: 页面URL
            max_scrolls: 最大滚动次数
            
        Returns:
            Set[str]: 作品链接集合
        """
        self.logger.info(f"🔄 开始滚动提取: {url} (最大滚动: {max_scrolls}次)")
        
        try:
            # 导航到页面
            self.driver.get(url)
            self._wait_for_page_load()
            
            all_links = set()
            scroll_count = 0
            
            while scroll_count < max_scrolls:
                # 提取当前页面的链接
                current_links = self._extract_links_from_current_page()
                new_links = current_links - all_links
                
                if new_links:
                    all_links.update(new_links)
                    self.logger.info(f"📊 滚动 {scroll_count + 1}: 新增 {len(new_links)} 个链接")
                else:
                    self.logger.info(f"⏹️ 滚动 {scroll_count + 1}: 无新链接，停止滚动")
                    break
                
                # 滚动页面
                self._scroll_page()
                scroll_count += 1
                
                # 等待新内容加载
                time.sleep(2)
            
            self.logger.info(f"✅ 滚动提取完成: 总共 {len(all_links)} 个链接")
            return all_links
            
        except Exception as e:
            self.logger.error(f"❌ 滚动提取失败: {e}")
            return set()
    
    def _scroll_page(self) -> None:
        """滚动页面"""
        try:
            # 滚动到页面底部
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            
            # 等待滚动完成
            time.sleep(1)
            
            # 尝试点击"加载更多"按钮
            self._try_click_load_more()
            
        except Exception as e:
            self.logger.debug(f"滚动页面失败: {e}")
    
    def _try_click_load_more(self) -> None:
        """尝试点击加载更多按钮"""
        try:
            load_more_selectors = SelectorConfig.get_selectors('load_more')
            
            for selector in load_more_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            element.click()
                            self.logger.debug("🔄 点击了加载更多按钮")
                            time.sleep(1)
                            return
                except Exception:
                    continue
                    
        except Exception as e:
            self.logger.debug(f"点击加载更多失败: {e}")
    
    def _cache_page_links(self, url: str, links: Set[str]) -> None:
        """缓存页面链接"""
        try:
            # 限制缓存大小
            if len(self._page_cache) >= self._cache_max_size:
                # 移除最旧的缓存项
                oldest_key = next(iter(self._page_cache))
                del self._page_cache[oldest_key]
            
            self._page_cache[url] = links.copy()
            
        except Exception as e:
            self.logger.debug(f"缓存页面链接失败: {e}")
    
    def clear_cache(self) -> None:
        """清理缓存"""
        cache_size = len(self._page_cache)
        self._page_cache.clear()
        self.logger.info(f"🧹 页面链接缓存已清理: {cache_size} 个缓存项")
    
    def get_cache_stats(self) -> dict:
        """获取缓存统计"""
        return {
            'cache_size': len(self._page_cache),
            'max_cache_size': self._cache_max_size,
            'cache_usage_percent': (len(self._page_cache) / self._cache_max_size) * 100
        }
