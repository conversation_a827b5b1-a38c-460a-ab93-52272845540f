"""
组件管理器

负责管理爬虫的各种组件，包括管理器、处理器等
"""

import logging
from typing import Optional, Callable

from .spider_context import SpiderContext
from .authentication_manager import AuthenticationManager
from ..utils.cache_manager import CacheManager
from ..utils.monitoring import MemoryMonitor, ResourceMonitor
from ..utils.selenium_utils import SeleniumDriver
from ..processors.artwork_processor import ArtworkProcessor
from ..handlers.download_mode_handler import DownloadModeHandler
from ..managers.resource_manager import ResourceManager
from ..managers.path_manager import PathManager


class ComponentManager:
    """组件管理器 - 管理各种功能组件"""
    
    def __init__(self, context: SpiderContext, auth_manager: AuthenticationManager):
        """
        初始化组件管理器
        
        Args:
            context: 爬虫上下文
            auth_manager: 认证管理器
        """
        self.context = context
        self.auth_manager = auth_manager
        self.logger = logging.getLogger(__name__)
        
        # 初始化核心组件
        self._init_core_components()
        
        # 延迟初始化的组件
        self.selenium_driver: Optional[SeleniumDriver] = None
        self.artwork_processor: Optional[ArtworkProcessor] = None
        self.download_mode_handler: Optional[DownloadModeHandler] = None
    
    def _init_core_components(self):
        """初始化核心组件"""
        # 缓存管理器
        max_cache_size = getattr(self.context.spider_config, 'max_cache_size', 1000)
        self.cache_manager = CacheManager(max_cache_size)
        
        # 资源管理器
        self.resource_manager = ResourceManager()
        self.resource_manager.register_cache_manager(self.cache_manager)
        
        # 路径管理器
        self.path_manager = PathManager(self.context.download_config)
        
        # 内存监控组件
        self.memory_monitor = MemoryMonitor(check_interval=60.0)
        self.resource_monitor = ResourceMonitor(check_interval=60.0)
        
        self.logger.info("核心组件初始化完成")
    
    def ensure_selenium_driver(self) -> bool:
        """确保Selenium驱动器已初始化"""
        self.logger.info(f"检查Selenium驱动器状态: 已存在={self.selenium_driver is not None}, 已认证={self.auth_manager.is_authenticated}")
        
        if not self.selenium_driver:
            # 即使未认证也尝试创建驱动器
            cookies_to_use = self.auth_manager.cookies if self.auth_manager.is_authenticated else []
            
            self.logger.info(f"正在初始化Selenium驱动器... (使用Cookie: {len(cookies_to_use) > 0})")
            try:
                self.selenium_driver = SeleniumDriver(cookies_to_use, self.context.config_manager)
                self.resource_manager.register_selenium_driver(self.selenium_driver)
                self.logger.info("Selenium驱动器初始化成功")
                
                # 如果没有认证，给出提示
                if not self.auth_manager.is_authenticated:
                    self.logger.warning("⚠️ 未找到保存的登录信息")
                    self.logger.warning("如果页面要求登录，请在浏览器中手动登录")
                    self.resource_manager._notify_status("⚠️ 未找到登录信息，可能需要在浏览器中登录")
                
            except Exception as e:
                self.logger.error(f"Selenium驱动器初始化失败: {e}")
                return False
        else:
            self.logger.info("Selenium驱动器已存在，跳过初始化")
        
        return True
    
    def init_processors(self):
        """初始化处理器（需要在API服务创建后调用）"""
        if self.context.api_service and not self.artwork_processor:
            # 作品处理器
            self.artwork_processor = ArtworkProcessor(
                self.context.api_service,
                self.context.download_config,
                self.cache_manager
            )
            
            # 下载模式处理器
            if self.selenium_driver:
                self.download_mode_handler = DownloadModeHandler(
                    self.context.download_config,
                    self.selenium_driver,
                    self.artwork_processor
                )
            
            self.logger.info("处理器初始化完成")
    
    def setup_services_integration(self):
        """设置服务集成"""
        # 注册API服务到资源管理器
        if self.context.api_service:
            self.resource_manager.register_api_service(self.context.api_service)
        
        # 注册下载服务到资源管理器
        if self.context.download_service:
            self.resource_manager.register_download_service(self.context.download_service)
        
        # 确保使用ResourceManager中的下载服务实例
        if self.resource_manager.download_service:
            self.context.download_service = self.resource_manager.download_service
            self.logger.info("✅ 使用ResourceManager中的下载服务实例")
    
    def set_progress_callback(self, callback: Callable) -> None:
        """设置进度回调函数"""
        self.resource_manager.set_progress_callback(callback)
    
    def set_status_callback(self, callback: Callable) -> None:
        """设置状态回调函数"""
        self.resource_manager.set_status_callback(callback)
    
    def start_monitoring(self):
        """启动监控"""
        self.memory_monitor.start_monitoring()
        self.logger.info("🔍 内存监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        if hasattr(self, 'memory_monitor'):
            self.memory_monitor.stop_monitoring()
            self.logger.info("⏹️ 内存监控已停止")
    
    def cleanup_all_resources(self):
        """清理所有资源"""
        try:
            # 停止内存监控
            self.stop_monitoring()
            
            # 获取最终内存统计
            if hasattr(self, 'memory_monitor'):
                memory_stats = self.memory_monitor.get_memory_stats()
                if memory_stats:
                    self.logger.info(f"📊 最终内存统计: {memory_stats['current_memory_mb']:.1f}MB")
                self.memory_monitor.cleanup_resources()
            
            # 检查资源使用情况
            if hasattr(self, 'resource_monitor'):
                resource_stats = self.resource_monitor.get_current_stats()
                if resource_stats:
                    self.logger.info(f"📊 最终资源统计: {resource_stats}")
                self.resource_monitor.cleanup_resources()
            
            # 清理主要资源
            self.resource_manager.cleanup_all_resources()
            
            # 强制垃圾回收
            import gc
            gc.collect()
            self.logger.debug("🗑️ 最终垃圾回收已执行")
            
        except Exception as e:
            self.logger.error(f"❌ 清理资源时出错: {e}")
    
    def get_component_status(self) -> dict:
        """获取组件状态"""
        return {
            'selenium_driver_available': self.selenium_driver is not None,
            'artwork_processor_available': self.artwork_processor is not None,
            'download_mode_handler_available': self.download_mode_handler is not None,
            'memory_monitoring_active': hasattr(self, 'memory_monitor') and self.memory_monitor._is_monitoring,
            **self.resource_manager.get_resource_stats()
        }
