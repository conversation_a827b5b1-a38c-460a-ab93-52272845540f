"""
下载状态管理器

专门负责管理下载过程的状态，包括进度跟踪、暂停恢复等功能
"""

import logging
import time
import json
import os
from typing import Dict, Any, List, Optional, Callable
from pathlib import Path

from ....models.artwork import Artwork, ArtworkStatus


class DownloadStateManager:
    """下载状态管理器 - 专注状态管理功能"""
    
    def __init__(self, checkpoint_dir: str = "checkpoints"):
        """
        初始化下载状态管理器
        
        Args:
            checkpoint_dir: 断点文件目录
        """
        self.logger = logging.getLogger(__name__)
        self.checkpoint_dir = checkpoint_dir
        
        # 确保断点目录存在
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # 下载状态
        self._state = {
            'status': 'idle',  # idle, running, paused, stopping, stopped
            'current_batch': 0,
            'total_batches': 0,
            'completed_artworks': [],
            'failed_artworks': [],
            'pending_artworks': [],
            'current_artwork_id': None,
            'start_time': None,
            'pause_time': None,
            'resume_time': None,
            'total_artworks': 0,
            'processed_artworks': 0
        }
        
        # 控制标志
        self._should_stop = False
        self._is_stopping = False
        self._is_paused = False
        
        # 回调函数
        self._progress_callback: Optional[Callable] = None
        self._status_callback: Optional[Callable] = None
    
    def set_progress_callback(self, callback: Callable) -> None:
        """设置进度回调函数"""
        self._progress_callback = callback
    
    def set_status_callback(self, callback: Callable) -> None:
        """设置状态回调函数"""
        self._status_callback = callback
    
    def get_state(self) -> Dict[str, Any]:
        """获取当前下载状态"""
        return self._state.copy()
    
    def update_state(self, **kwargs) -> None:
        """
        更新下载状态
        
        Args:
            **kwargs: 要更新的状态字段
        """
        for key, value in kwargs.items():
            if key in self._state:
                self._state[key] = value
                self.logger.debug(f"📊 状态更新: {key} = {value}")
        
        # 触发状态回调
        if self._status_callback:
            try:
                self._status_callback(self._state.copy())
            except Exception as e:
                self.logger.error(f"❌ 状态回调执行失败: {e}")
    
    def start_download(self, total_artworks: int) -> None:
        """
        开始下载
        
        Args:
            total_artworks: 总作品数
        """
        self.logger.info(f"🚀 开始下载，总作品数: {total_artworks}")
        
        self._should_stop = False
        self._is_stopping = False
        self._is_paused = False
        
        self.update_state(
            status='running',
            total_artworks=total_artworks,
            processed_artworks=0,
            start_time=time.time(),
            pause_time=None,
            resume_time=None,
            completed_artworks=[],
            failed_artworks=[],
            pending_artworks=[]
        )
    
    def pause_download(self) -> None:
        """暂停下载"""
        if self._state['status'] == 'running':
            self.logger.info("⏸️ 暂停下载")
            self._is_paused = True
            self.update_state(
                status='paused',
                pause_time=time.time()
            )
    
    def resume_download(self) -> None:
        """恢复下载"""
        if self._state['status'] == 'paused':
            self.logger.info("▶️ 恢复下载")
            self._is_paused = False
            self.update_state(
                status='running',
                resume_time=time.time()
            )
    
    def stop_download(self) -> None:
        """停止下载"""
        self.logger.info("🛑 停止下载")
        self._should_stop = True
        self._is_stopping = True
        self.update_state(status='stopping')
    
    def complete_download(self) -> None:
        """完成下载"""
        self.logger.info("✅ 下载完成")
        self.update_state(status='stopped')
        self._clear_checkpoint()
    
    def reset_state(self) -> None:
        """重置状态"""
        self.logger.info("🔄 重置下载状态")
        
        self._should_stop = False
        self._is_stopping = False
        self._is_paused = False
        
        self._state = {
            'status': 'idle',
            'current_batch': 0,
            'total_batches': 0,
            'completed_artworks': [],
            'failed_artworks': [],
            'pending_artworks': [],
            'current_artwork_id': None,
            'start_time': None,
            'pause_time': None,
            'resume_time': None,
            'total_artworks': 0,
            'processed_artworks': 0
        }
        
        self._clear_checkpoint()
    
    def should_stop(self) -> bool:
        """检查是否应该停止"""
        return self._should_stop
    
    def is_paused(self) -> bool:
        """检查是否暂停"""
        return self._is_paused
    
    def is_stopping(self) -> bool:
        """检查是否正在停止"""
        return self._is_stopping
    
    def add_completed_artwork(self, artwork_id: str) -> None:
        """添加已完成的作品"""
        if artwork_id not in self._state['completed_artworks']:
            self._state['completed_artworks'].append(artwork_id)
            self._state['processed_artworks'] += 1
            self._trigger_progress_callback()
    
    def add_failed_artwork(self, artwork_id: str) -> None:
        """添加失败的作品"""
        if artwork_id not in self._state['failed_artworks']:
            self._state['failed_artworks'].append(artwork_id)
            self._state['processed_artworks'] += 1
            self._trigger_progress_callback()
    
    def set_current_artwork(self, artwork_id: str) -> None:
        """设置当前处理的作品"""
        self.update_state(current_artwork_id=artwork_id)
    
    def _trigger_progress_callback(self) -> None:
        """触发进度回调"""
        if self._progress_callback:
            try:
                progress_data = {
                    'processed': self._state['processed_artworks'],
                    'total': self._state['total_artworks'],
                    'completed': len(self._state['completed_artworks']),
                    'failed': len(self._state['failed_artworks']),
                    'current_artwork': self._state['current_artwork_id']
                }
                self._progress_callback(progress_data)
            except Exception as e:
                self.logger.error(f"❌ 进度回调执行失败: {e}")
    
    def save_checkpoint(self, artworks: List[Artwork]) -> None:
        """
        保存断点信息
        
        Args:
            artworks: 作品列表
        """
        try:
            checkpoint_file = os.path.join(self.checkpoint_dir, "download_checkpoint.json")
            checkpoint_data = {
                'state': self._state,
                'artworks': [
                    {
                        'id': artwork.id,
                        'status': artwork.status.value if artwork.status else 'pending',
                        'local_path': artwork.local_path
                    }
                    for artwork in artworks
                ],
                'timestamp': time.time()
            }
            
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
            
            self.logger.debug(f"💾 断点已保存: {checkpoint_file}")
        
        except Exception as e:
            self.logger.error(f"❌ 保存断点失败: {e}")
    
    def load_checkpoint(self) -> Optional[Dict[str, Any]]:
        """
        加载断点信息
        
        Returns:
            Optional[Dict[str, Any]]: 断点数据，如果不存在则返回None
        """
        try:
            checkpoint_file = os.path.join(self.checkpoint_dir, "download_checkpoint.json")
            
            if not os.path.exists(checkpoint_file):
                return None
            
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
            
            self.logger.info(f"📂 断点已加载: {checkpoint_file}")
            return checkpoint_data
        
        except Exception as e:
            self.logger.error(f"❌ 加载断点失败: {e}")
            return None
    
    def _clear_checkpoint(self) -> None:
        """清除断点文件"""
        try:
            checkpoint_file = os.path.join(self.checkpoint_dir, "download_checkpoint.json")
            if os.path.exists(checkpoint_file):
                os.remove(checkpoint_file)
                self.logger.debug("🗑️ 断点文件已清除")
        except Exception as e:
            self.logger.error(f"❌ 清除断点文件失败: {e}")
    
    def get_download_stats(self) -> Dict[str, Any]:
        """
        获取下载统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        elapsed_time = 0
        if self._state['start_time']:
            if self._state['status'] in ['paused', 'stopped']:
                if self._state['pause_time']:
                    elapsed_time = self._state['pause_time'] - self._state['start_time']
                else:
                    elapsed_time = time.time() - self._state['start_time']
            elif self._state['status'] == 'running':
                elapsed_time = time.time() - self._state['start_time']
        
        return {
            'status': self._state['status'],
            'total_artworks': self._state['total_artworks'],
            'processed_artworks': self._state['processed_artworks'],
            'completed_count': len(self._state['completed_artworks']),
            'failed_count': len(self._state['failed_artworks']),
            'elapsed_time': elapsed_time,
            'progress_percentage': (
                (self._state['processed_artworks'] / self._state['total_artworks'] * 100)
                if self._state['total_artworks'] > 0 else 0
            )
        }
