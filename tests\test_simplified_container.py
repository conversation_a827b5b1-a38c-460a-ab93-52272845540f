"""
简化依赖注入容器测试

测试新的简化依赖注入容器功能
"""

import pytest
import logging
from unittest.mock import Mock, patch
from typing import Dict, Any

from src.pixiv_spider.container import (
    ServiceContainer, 
    ServiceLifetime, 
    ServiceBuilder,
    ServiceProvider
)
from src.pixiv_spider.config.config_manager import ConfigManager


class MockService:
    """模拟服务类"""
    def __init__(self, config_manager=None):
        self.config_manager = config_manager
        self.initialized = True


class MockComplexService:
    """模拟复杂服务类"""
    def __init__(self, dependency: MockService):
        self.dependency = dependency
        self.initialized = True


class TestSimplifiedServiceContainer:
    """测试简化的服务容器"""
    
    def setup_method(self):
        """设置测试环境"""
        self.container = ServiceContainer()
    
    def test_register_singleton_service(self):
        """测试注册单例服务"""
        # 注册单例服务
        self.container.register_singleton(MockService)
        
        # 获取两次实例
        instance1 = self.container.get(MockService)
        instance2 = self.container.get(MockService)
        
        # 验证是同一个实例
        assert instance1 is instance2
        assert instance1.initialized
    
    def test_register_transient_service(self):
        """测试注册瞬态服务"""
        # 注册瞬态服务
        self.container.register_transient(MockService)
        
        # 获取两次实例
        instance1 = self.container.get(MockService)
        instance2 = self.container.get(MockService)
        
        # 验证是不同的实例
        assert instance1 is not instance2
        assert instance1.initialized
        assert instance2.initialized
    
    def test_register_scoped_service(self):
        """测试注册作用域服务"""
        # 注册作用域服务
        self.container.register_scoped(MockService)
        
        # 获取两次实例
        instance1 = self.container.get(MockService)
        instance2 = self.container.get(MockService)
        
        # 在同一作用域内应该是同一个实例
        assert instance1 is instance2
        
        # 清空作用域后应该是新实例
        self.container.clear_scoped()
        instance3 = self.container.get(MockService)
        assert instance1 is not instance3
    
    def test_register_instance(self):
        """测试注册服务实例"""
        # 创建实例
        mock_instance = MockService()
        
        # 注册实例
        self.container.register_instance(MockService, mock_instance)
        
        # 获取实例
        retrieved_instance = self.container.get(MockService)
        
        # 验证是同一个实例
        assert retrieved_instance is mock_instance
    
    def test_register_factory(self):
        """测试注册工厂函数"""
        # 定义工厂函数
        def mock_factory(container):
            return MockService(container.get_config_manager())
        
        # 注册工厂
        self.container.register_transient(MockService, mock_factory)
        
        # 获取实例
        instance = self.container.get(MockService)
        
        # 验证实例正确创建
        assert isinstance(instance, MockService)
        assert instance.config_manager is not None
    
    def test_service_not_found(self):
        """测试获取未注册的服务"""
        with pytest.raises(ValueError, match="服务未注册"):
            self.container.get("non_existent_service")
    
    def test_try_get_service(self):
        """测试尝试获取服务"""
        # 注册服务
        self.container.register_singleton(MockService)
        
        # 尝试获取已注册的服务
        instance = self.container.try_get(MockService)
        assert instance is not None
        assert isinstance(instance, MockService)
        
        # 尝试获取未注册的服务
        result = self.container.try_get("non_existent_service")
        assert result is None
    
    def test_has_service(self):
        """测试检查服务是否存在"""
        # 注册服务
        self.container.register_singleton(MockService)
        
        # 检查已注册的服务
        assert self.container.has(MockService)
        assert self.container.has("MockService")
        
        # 检查未注册的服务
        assert not self.container.has("non_existent_service")
    
    def test_get_registered_services(self):
        """测试获取已注册的服务列表"""
        # 注册不同类型的服务
        self.container.register_singleton(MockService, name="singleton_service")
        self.container.register_transient(MockService, name="transient_service")
        
        # 获取服务列表
        services = self.container.get_registered_services()
        
        # 验证服务列表
        assert "singleton_service" in services
        assert "transient_service" in services
        assert services["singleton_service"] == "singleton"
        assert services["transient_service"] == "transient"


class TestServiceBuilder:
    """测试服务构建器"""
    
    def test_service_builder_chain(self):
        """测试服务构建器链式调用"""
        # 使用构建器创建容器
        container = (ServiceBuilder()
                    .with_default_services()
                    .build())
        
        # 验证容器创建成功
        assert isinstance(container, ServiceContainer)
        
        # 验证默认服务已注册
        config_manager = container.get_config_manager()
        assert isinstance(config_manager, ConfigManager)
    
    def test_custom_config_manager(self):
        """测试自定义配置管理器"""
        # 创建自定义配置管理器
        custom_config = ConfigManager()
        
        # 使用构建器设置自定义配置
        container = (ServiceBuilder()
                    .with_custom_config_manager(custom_config)
                    .with_default_services()
                    .build())
        
        # 验证使用了自定义配置管理器
        retrieved_config = container.get_config_manager()
        assert retrieved_config is custom_config


class TestServiceProvider:
    """测试服务提供者"""
    
    def setup_method(self):
        """设置测试环境"""
        self.container = ServiceContainer()
        self.provider = ServiceProvider(self.container)
    
    def test_get_auth_service(self):
        """测试获取认证服务"""
        with patch('src.pixiv_spider.container.service_factory.AuthService') as mock_auth:
            mock_instance = Mock()
            mock_auth.return_value = mock_instance
            
            # 获取认证服务
            auth_service = self.provider.get_auth_service()
            
            # 验证服务创建
            assert auth_service is mock_instance
            
            # 再次获取应该返回缓存的实例
            auth_service2 = self.provider.get_auth_service()
            assert auth_service2 is auth_service
    
    def test_get_cache_service(self):
        """测试获取缓存服务"""
        with patch('src.pixiv_spider.container.service_factory.CacheService') as mock_cache:
            mock_instance = Mock()
            mock_cache.return_value = mock_instance
            
            # 获取缓存服务
            cache_service = self.provider.get_cache_service()
            
            # 验证服务创建
            assert cache_service is mock_instance
    
    def test_setup_with_cookies(self):
        """测试使用cookies设置服务"""
        cookies = {"session": "test_session"}
        
        with patch('src.pixiv_spider.container.service_factory.PixivApiService') as mock_api, \
             patch('src.pixiv_spider.container.service_factory.DownloadService') as mock_download:
            
            mock_api_instance = Mock()
            mock_download_instance = Mock()
            mock_api.return_value = mock_api_instance
            mock_download.return_value = mock_download_instance
            
            # 设置服务
            self.provider.setup_with_cookies(cookies)
            
            # 验证API服务创建
            api_service = self.provider.get_api_service()
            assert api_service is mock_api_instance
            
            # 验证下载服务创建
            download_service = self.provider.get_download_service()
            assert download_service is mock_download_instance
    
    def test_clear_cache(self):
        """测试清空服务缓存"""
        with patch('src.pixiv_spider.container.service_factory.AuthService') as mock_auth:
            mock_instance = Mock()
            mock_auth.return_value = mock_instance
            
            # 获取服务
            auth_service = self.provider.get_auth_service()
            assert auth_service is mock_instance
            
            # 清空缓存
            self.provider.clear_cache()
            
            # 再次获取应该创建新实例
            auth_service2 = self.provider.get_auth_service()
            assert mock_auth.call_count == 2  # 应该被调用两次
    
    def test_get_service_status(self):
        """测试获取服务状态"""
        # 初始状态
        status = self.provider.get_service_status()
        assert not any(status.values())
        
        # 获取认证服务后
        with patch('src.pixiv_spider.container.service_factory.AuthService'):
            self.provider.get_auth_service()
            status = self.provider.get_service_status()
            assert status['auth_service']
            assert not status['api_service']


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
