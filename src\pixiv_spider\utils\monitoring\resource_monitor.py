"""
资源监控器

监控系统资源使用情况和资源泄漏
"""

import logging
import psutil
import threading
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass


@dataclass
class ResourceSnapshot:
    """资源快照"""
    timestamp: float
    cpu_percent: float
    memory_mb: float
    disk_usage_percent: float
    network_io_bytes: int
    open_files: int
    thread_count: int


class ResourceMonitor:
    """资源监控器"""
    
    def __init__(self, check_interval: float = 60.0):
        """
        初始化资源监控器
        
        Args:
            check_interval: 检查间隔(秒)
        """
        self.logger = logging.getLogger(__name__)
        self.check_interval = check_interval
        
        # 资源快照历史
        self.snapshots: List[ResourceSnapshot] = []
        self.max_snapshots = 50
        
        # 监控线程
        self._monitor_thread: Optional[threading.Thread] = None
        self._is_monitoring = False
        
        # 资源阈值
        self.cpu_threshold = 80.0  # CPU使用率阈值
        self.memory_threshold = 80.0  # 内存使用率阈值
        self.disk_threshold = 90.0  # 磁盘使用率阈值
        
    def start_monitoring(self) -> None:
        """开始资源监控"""
        if self._is_monitoring:
            self.logger.warning("资源监控已在运行")
            return
            
        self._is_monitoring = True
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        self.logger.info(f"📊 资源监控已启动，检查间隔: {self.check_interval}秒")
        
    def stop_monitoring(self) -> None:
        """停止资源监控"""
        if not self._is_monitoring:
            return
            
        self._is_monitoring = False
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=5)
        self.logger.info("⏹️ 资源监控已停止")
        
    def _monitor_loop(self) -> None:
        """监控循环"""
        while self._is_monitoring:
            try:
                snapshot = self._take_snapshot()
                self._add_snapshot(snapshot)
                self._check_resource_usage(snapshot)
                time.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f"资源监控异常: {e}")
                time.sleep(self.check_interval)
                
    def _take_snapshot(self) -> ResourceSnapshot:
        """获取资源快照"""
        try:
            process = psutil.Process()
            
            # CPU使用率
            cpu_percent = process.cpu_percent()
            
            # 内存使用
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            # 磁盘使用率
            disk_usage = psutil.disk_usage('/')
            disk_percent = (disk_usage.used / disk_usage.total) * 100
            
            # 网络IO
            net_io = psutil.net_io_counters()
            network_io_bytes = net_io.bytes_sent + net_io.bytes_recv
            
            # 打开的文件数
            try:
                open_files = len(process.open_files())
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                open_files = 0
            
            # 线程数
            thread_count = threading.active_count()
            
            return ResourceSnapshot(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_mb=memory_mb,
                disk_usage_percent=disk_percent,
                network_io_bytes=network_io_bytes,
                open_files=open_files,
                thread_count=thread_count
            )
            
        except Exception as e:
            self.logger.error(f"获取资源快照失败: {e}")
            # 返回默认快照
            return ResourceSnapshot(
                timestamp=time.time(),
                cpu_percent=0.0,
                memory_mb=0.0,
                disk_usage_percent=0.0,
                network_io_bytes=0,
                open_files=0,
                thread_count=0
            )
            
    def _add_snapshot(self, snapshot: ResourceSnapshot) -> None:
        """添加快照"""
        self.snapshots.append(snapshot)
        
        # 保持快照数量在限制内
        if len(self.snapshots) > self.max_snapshots:
            self.snapshots.pop(0)
            
    def _check_resource_usage(self, snapshot: ResourceSnapshot) -> None:
        """检查资源使用情况"""
        warnings = []
        
        # 检查CPU使用率
        if snapshot.cpu_percent > self.cpu_threshold:
            warnings.append(f"CPU使用率过高: {snapshot.cpu_percent:.1f}%")
        
        # 检查内存使用率
        system_memory = psutil.virtual_memory()
        memory_percent = (snapshot.memory_mb / (system_memory.total / 1024 / 1024)) * 100
        if memory_percent > self.memory_threshold:
            warnings.append(f"内存使用率过高: {memory_percent:.1f}%")
        
        # 检查磁盘使用率
        if snapshot.disk_usage_percent > self.disk_threshold:
            warnings.append(f"磁盘使用率过高: {snapshot.disk_usage_percent:.1f}%")
        
        # 检查打开文件数
        if snapshot.open_files > 1000:
            warnings.append(f"打开文件数过多: {snapshot.open_files}")
        
        # 记录警告
        for warning in warnings:
            self.logger.warning(f"⚠️ 资源警告: {warning}")
            
    def get_current_stats(self) -> Dict[str, Any]:
        """获取当前资源统计"""
        if not self.snapshots:
            return {}
            
        latest = self.snapshots[-1]
        
        return {
            'cpu_percent': latest.cpu_percent,
            'memory_mb': latest.memory_mb,
            'disk_usage_percent': latest.disk_usage_percent,
            'network_io_bytes': latest.network_io_bytes,
            'open_files': latest.open_files,
            'thread_count': latest.thread_count,
            'timestamp': latest.timestamp
        }
        
    def get_resource_trends(self) -> Dict[str, Any]:
        """获取资源使用趋势"""
        if len(self.snapshots) < 2:
            return {}
            
        first = self.snapshots[0]
        latest = self.snapshots[-1]
        
        time_diff = latest.timestamp - first.timestamp
        
        return {
            'cpu_trend': latest.cpu_percent - first.cpu_percent,
            'memory_trend_mb': latest.memory_mb - first.memory_mb,
            'network_io_trend': latest.network_io_bytes - first.network_io_bytes,
            'open_files_trend': latest.open_files - first.open_files,
            'thread_trend': latest.thread_count - first.thread_count,
            'time_span_seconds': time_diff,
            'snapshots_count': len(self.snapshots)
        }
        
    def get_peak_usage(self) -> Dict[str, Any]:
        """获取峰值使用情况"""
        if not self.snapshots:
            return {}
            
        peak_cpu = max(s.cpu_percent for s in self.snapshots)
        peak_memory = max(s.memory_mb for s in self.snapshots)
        peak_files = max(s.open_files for s in self.snapshots)
        peak_threads = max(s.thread_count for s in self.snapshots)
        
        return {
            'peak_cpu_percent': peak_cpu,
            'peak_memory_mb': peak_memory,
            'peak_open_files': peak_files,
            'peak_thread_count': peak_threads
        }
        
    def cleanup_resources(self) -> None:
        """清理监控器资源"""
        try:
            self.stop_monitoring()
            self.snapshots.clear()
            self.logger.info("✅ 资源监控器已清理")
        except Exception as e:
            self.logger.error(f"❌ 清理资源监控器失败: {e}")
            
    def __del__(self):
        """析构函数"""
        try:
            self.cleanup_resources()
        except Exception:
            pass
