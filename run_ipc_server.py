#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pixiv Spider IPC Server 启动器

启动基于IPC通信的后端服务器，为Electron GUI提供服务
通过stdin/stdout进行JSON消息通信
"""

import sys
import os
import logging
import signal
import atexit
from pathlib import Path

# 添加源代码路径
current_dir = Path(__file__).parent
src_path = current_dir / "src"
sys.path.insert(0, str(src_path))

# 确保pixiv_spider包能被找到
pixiv_spider_path = src_path / "pixiv_spider"
if pixiv_spider_path.exists():
    sys.path.insert(0, str(src_path))
else:
    # 如果在不同的目录结构中，尝试其他路径
    alt_path = current_dir / "pixiv_spider"
    if alt_path.exists():
        sys.path.insert(0, str(current_dir))

# 调试信息（仅在开发模式下显示）
# print(f"Current directory: {current_dir}", file=sys.stderr)
# print(f"Python path: {sys.path[:3]}", file=sys.stderr)

def main():
    """主函数"""
    api = None

    def cleanup_handler(signum=None, frame=None):
        """清理处理器"""
        if api:
            try:
                print("🧹 正在清理IPC服务器资源...", file=sys.stderr)

                # 设置停止标志
                api.running = False

                # 清理资源
                api._cleanup_resources()

                # 注意：不再进行全局浏览器进程清理
                # 现在由SeleniumDriver精确清理自己控制的进程
                print("ℹ️ 浏览器进程清理已由SeleniumDriver精确处理", file=sys.stderr)

                print("✅ IPC服务器资源清理完成", file=sys.stderr)

            except Exception as e:
                print(f"❌ 清理资源失败: {e}", file=sys.stderr)
                import traceback
                print(f"清理异常堆栈: {traceback.format_exc()}", file=sys.stderr)

    # 注册信号处理器
    signal.signal(signal.SIGINT, cleanup_handler)
    signal.signal(signal.SIGTERM, cleanup_handler)

    # Windows特有信号
    if hasattr(signal, 'SIGBREAK'):
        signal.signal(signal.SIGBREAK, cleanup_handler)

    atexit.register(cleanup_handler)

    try:
        # 设置日志输出，避免与stdout通信冲突
        # 确保stderr使用UTF-8编码
        import codecs
        import locale

        # 设置系统编码为UTF-8
        if hasattr(sys.stderr, 'reconfigure'):
            sys.stderr.reconfigure(encoding='utf-8', errors='replace')
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8', errors='replace')

        # 设置默认编码
        try:
            locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
        except locale.Error:
            try:
                locale.setlocale(locale.LC_ALL, 'Chinese_China.UTF-8')
            except locale.Error:
                pass  # 如果设置失败，使用默认编码

        logging.basicConfig(
            level=logging.INFO,  # 临时提高日志级别以便调试
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            stream=sys.stderr,
            encoding='utf-8',
            errors='replace'
        )

        # 导入IPC API服务器
        try:
            from pixiv_spider.api_server import PixivSpiderAPI
            # print("Successfully imported PixivSpiderAPI", file=sys.stderr)
        except ImportError as e:
            print(f"Import error: {e}", file=sys.stderr)
            print(f"Available modules in sys.path[0]: {list(Path(sys.path[0]).iterdir()) if Path(sys.path[0]).exists() else 'Path not found'}", file=sys.stderr)
            return

        # 创建并启动IPC服务器
        api = PixivSpiderAPI()

        # 不输出启动信息，通过JSON事件通知
        # print("IPC server ready", file=sys.stderr)

        # 运行服务器
        api.run()

    except KeyboardInterrupt:
        pass  # 静默退出
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
    finally:
        cleanup_handler()

if __name__ == "__main__":
    main()
