<template>
  <el-card class="login-card modern-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="header-left">
          <el-icon class="header-icon"><User /></el-icon>
          <span class="header-title">登录状态</span>
        </div>
      </div>
    </template>

    <div class="login-content">
      <div class="status-display">
        <div class="status-indicator" :class="statusClass">
          <el-icon>
            <component :is="statusIcon" />
          </el-icon>
          {{ loginStatus }}
        </div>
      </div>

      <div class="login-actions modern-button-group">
        <el-button
          type="primary"
          @click="checkLogin"
          :loading="checking"
          size="small"
        >
          <el-icon><Refresh /></el-icon>
          检查登录
        </el-button>

        <el-button
          type="warning"
          @click="showLoginDialog"
          :loading="logging"
          size="small"
        >
          <el-icon><Key /></el-icon>
          {{ isLoggedIn ? '重新登录' : '立即登录' }}
        </el-button>
      </div>
    </div>

    <!-- 登录对话框 -->
    <LoginDialog
      v-model="loginDialogVisible"
      @login-success="onLoginSuccess"
      @login-failed="onLoginFailed"
      @login-cancelled="onLoginCancelled"
    />
  </el-card>
</template>

<script>
import { mapState } from 'vuex'
import { User, Refresh, Key, SuccessFilled, WarningFilled, Loading } from '@element-plus/icons-vue'
import LoginDialog from './LoginDialog.vue'
import errorHandlerService from '../services/error-handler.js'

export default {
  name: 'LoginStatus',
  inject: ['$api', '$ipc'],
  components: {
    User,
    Refresh,
    Key,
    SuccessFilled,
    WarningFilled,
    Loading,
    LoginDialog
  },
  data() {
    return {
      checking: false,
      logging: false,
      loginDialogVisible: false
    }
  },
  computed: {
    ...mapState(['isLoggedIn']),
    loginStatus() {
      const status = this.$store.state.loginStatus
      // 防止乱码，确保显示正确的中文状态
      if (!status || status.includes('�')) {
        return this.isLoggedIn ? '已登录' : '未登录'
      }
      // 将英文状态转换为中文
      const statusMap = {
        'Checking...': '检查中...',
        'Logged In': '已登录',
        'Not Logged In': '未登录',
        'Connection Failed': '连接失败',
        'Login Success': '登录成功',
        'Login Failed': '登录失败'
      }
      return statusMap[status] || (this.isLoggedIn ? '已登录' : '未登录')
    },
    statusType() {
      if (this.loginStatus.includes('已登录')) return 'success'
      if (this.loginStatus.includes('失败')) return 'danger'
      return 'warning'
    },
    statusIcon() {
      if (this.loginStatus.includes('已登录')) return SuccessFilled
      if (this.loginStatus.includes('失败')) return WarningFilled
      return Loading
    },
    statusClass() {
      if (this.loginStatus.includes('已登录')) return 'success'
      if (this.loginStatus.includes('失败')) return 'danger'
      if (this.loginStatus.includes('检查中')) return 'info'
      return 'warning'
    }
  },
  methods: {
    async checkLogin() {
      this.checking = true
      try {
        // 调用后端API检查登录状态
        const response = await this.$api.getAuthStatus()
        this.$store.commit('setLoginStatus', {
          isLoggedIn: response.authenticated || false,
          status: response.message || (response.authenticated ? '已登录' : '未登录')
        })
      } catch (error) {
        console.error('检查登录状态失败:', error)
        this.$store.commit('setLoginStatus', {
          isLoggedIn: false,
          status: '连接失败'
        })
        this.$message.error('检查登录状态失败')
      } finally {
        this.checking = false
      }
    },
    
    showLoginDialog() {
      this.loginDialogVisible = true
    },

    onLoginSuccess(response) {
      console.log('登录成功:', response)
      this.$store.commit('setLoginStatus', {
        isLoggedIn: true,
        status: '登录成功'
      })
      this.$message.success('登录成功！')
      this.loginDialogVisible = false

      // 重新检查登录状态
      this.checkLogin()
    },

    onLoginFailed(response) {
      console.error('登录失败:', response)
      this.$store.commit('setLoginStatus', {
        isLoggedIn: false,
        status: '登录失败'
      })
      this.$message.error('登录失败: ' + (response.message || response.error || '未知错误'))
    },

    onLoginCancelled() {
      console.log('用户取消登录')
      this.$message.info('登录已取消')
    }
  },
  
  mounted() {
    // 组件挂载时检查登录状态
    this.checkLogin()
  }
}
</script>

<style scoped>
.login-card {
  margin-bottom: 20px;
  background-color: var(--bg-primary);
  border-color: var(--border-color);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--text-primary);
}

.login-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.status-display {
  text-align: center;
  color: var(--text-primary);
}

.login-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.login-actions .el-button {
  flex: 1;
}
</style>
