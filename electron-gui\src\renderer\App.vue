<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App',
  mounted() {
    // 加载设置
    this.$store.dispatch('loadSettings')

    // 监听Python后端日志
    if (window.electronAPI) {
      // 新的分级日志监听
      window.electronAPI.onPythonInfo((event, data) => {
        this.$store.commit('addLog', data.trim())
      })

      window.electronAPI.onPythonWarning((event, data) => {
        this.$store.commit('addLog', `⚠️ ${data.trim()}`)
      })

      window.electronAPI.onPythonError((event, data) => {
        this.$store.commit('addLog', `❌ ${data.trim()}`)
      })

      // 保留兼容性：监听旧的事件
      window.electronAPI.onPythonLog((event, data) => {
        this.$store.commit('addLog', data.trim())
      })

      // 监听详细日志消息
      window.electronAPI.onLogMessage((event, data) => {
        const logMessage = `[${data.level}] ${data.message}`
        this.$store.commit('addLog', logMessage)
      })

      // 监听下载完成事件
      window.electronAPI.onDownloadComplete((event, data) => {
        console.log('🎉 收到下载完成事件:', data)
        this.$store.commit('setDownloading', false)
        this.$store.commit('addLog', '🎉 下载任务已完成！')

        // 显示完成统计
        if (data && data.stats) {
          const stats = data.stats
          const message = `下载完成 - 成功: ${stats.success || 0}, 跳过: ${stats.skipped || 0}, 失败: ${stats.failed || 0}`
          this.$store.commit('addLog', `📊 ${message}`)

          // 更新最终统计
          this.$store.commit('updateDownloadState', {
            stats: {
              total: stats.total || 0,
              completed: stats.success || 0,
              failed: stats.failed || 0,
              skipped: stats.skipped || 0
            }
          })
        }
      })

      // 监听进度更新事件
      window.electronAPI.onProgressUpdate((event, data) => {
        console.log('📊 收到进度更新:', data)
        console.log('📊 当前store状态:', this.$store.state.downloadProgress, this.$store.state.downloadStats)

        // 更新进度信息
        this.$store.commit('updateDownloadState', {
          progress: {
            current: data.current || 0,
            total: data.total || 0,
            percentage: data.percentage || 0,
            message: data.message || ''
          },
          stats: data.stats || {}
        })

        console.log('📊 更新后store状态:', this.$store.state.downloadProgress, this.$store.state.downloadStats)
      })

      // 添加测试按钮来模拟进度更新
      console.log('🧪 添加测试进度更新功能')
      window.testProgressUpdate = () => {
        console.log('🧪 模拟进度更新...')
        this.$store.commit('updateDownloadState', {
          progress: {
            current: 5,
            total: 10,
            percentage: 50,
            message: '测试进度更新'
          },
          stats: {
            success: 3,
            failed: 1,
            skipped: 1,
            total: 10
          }
        })
        console.log('🧪 测试进度更新完成')
      }
    }
  },
  beforeUnmount() {
    // 清理监听器
    if (window.electronAPI) {
      window.electronAPI.removeAllListeners('python-log')
      window.electronAPI.removeAllListeners('python-error')
      window.electronAPI.removeAllListeners('python-info')
      window.electronAPI.removeAllListeners('python-warning')
      window.electronAPI.removeAllListeners('log-message')
    }
  }
}
</script>

<style>
#app {
  height: 100vh;
  margin: 0;
  padding: 0;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}
</style>
