"""
缓存管理模块

提供LRU缓存和缓存管理功能
"""

import logging
from collections import OrderedDict
from typing import Any, Dict, Optional


class LRUCache:
    """简单的LRU缓存实现"""

    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache = OrderedDict()

    def get(self, key):
        if key in self.cache:
            # 移动到末尾（最近使用）
            self.cache.move_to_end(key)
            return self.cache[key]
        return None

    def put(self, key, value):
        if key in self.cache:
            # 更新现有键
            self.cache.move_to_end(key)
        elif len(self.cache) >= self.max_size:
            # 移除最久未使用的项
            self.cache.popitem(last=False)
        self.cache[key] = value

    def clear(self):
        self.cache.clear()

    def size(self):
        return len(self.cache)

    def __contains__(self, key):
        return key in self.cache

    def __len__(self):
        return len(self.cache)


class CacheManager:
    """缓存管理器"""

    def __init__(self, max_cache_size: int = 1000):
        """
        初始化缓存管理器
        
        Args:
            max_cache_size: 最大缓存大小
        """
        self.logger = logging.getLogger(__name__)
        self._artwork_cache = LRUCache(max_size=max_cache_size)
        self._detail_cache = LRUCache(max_size=max_cache_size // 2)  # 详情缓存稍小
        
    def get_artwork(self, artwork_id: int):
        """获取缓存的作品"""
        return self._artwork_cache.get(artwork_id)
    
    def put_artwork(self, artwork_id: int, artwork):
        """缓存作品"""
        self._artwork_cache.put(artwork_id, artwork)
    
    def get_detail(self, artwork_id: int):
        """获取缓存的作品详情"""
        return self._detail_cache.get(artwork_id)
    
    def put_detail(self, artwork_id: int, detail_data):
        """缓存作品详情"""
        self._detail_cache.put(artwork_id, detail_data)
    
    def clear_artwork_cache(self):
        """清理作品缓存"""
        cache_size = self._artwork_cache.size()
        self._artwork_cache.clear()
        self.logger.info(f"作品缓存已清理，释放了 {cache_size} 个缓存项")

    def clear_detail_cache(self):
        """清理详情缓存"""
        cache_size = self._detail_cache.size()
        self._detail_cache.clear()
        self.logger.info(f"详情缓存已清理，释放了 {cache_size} 个缓存项")

    def clear_all_caches(self):
        """清理所有缓存 - 增强版本，防止内存泄漏"""
        try:
            artwork_size = self._artwork_cache.size()
            detail_size = self._detail_cache.size()

            # 清理作品缓存
            self._artwork_cache.clear()

            # 清理详情缓存
            self._detail_cache.clear()

            # 强制垃圾回收
            import gc
            gc.collect()

            total_size = artwork_size + detail_size
            self.logger.info(f"所有缓存已清理，释放了 {total_size} 个缓存项")

        except Exception as e:
            self.logger.error(f"清理缓存失败: {e}")

    def __del__(self):
        """析构函数，确保缓存被清理"""
        try:
            self.clear_all_caches()
        except Exception:
            # 析构函数中不应该抛出异常
            pass

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'artwork_cache_size': self._artwork_cache.size(),
            'detail_cache_size': self._detail_cache.size(),
            'max_artwork_cache_size': self._artwork_cache.max_size,
            'max_detail_cache_size': self._detail_cache.max_size,
            'artwork_cache_usage_percent': (self._artwork_cache.size() / self._artwork_cache.max_size) * 100,
            'detail_cache_usage_percent': (self._detail_cache.size() / self._detail_cache.max_size) * 100
        }

    def is_artwork_cached(self, artwork_id: int) -> bool:
        """检查作品是否已缓存"""
        return artwork_id in self._artwork_cache

    def is_detail_cached(self, artwork_id: int) -> bool:
        """检查详情是否已缓存"""
        return artwork_id in self._detail_cache

    def get_cache_hit_info(self, artwork_ids: list) -> Dict[str, Any]:
        """获取缓存命中信息"""
        total_count = len(artwork_ids)
        artwork_hits = sum(1 for aid in artwork_ids if self.is_artwork_cached(aid))
        detail_hits = sum(1 for aid in artwork_ids if self.is_detail_cached(aid))
        
        return {
            'total_requests': total_count,
            'artwork_cache_hits': artwork_hits,
            'detail_cache_hits': detail_hits,
            'artwork_hit_rate': (artwork_hits / total_count) * 100 if total_count > 0 else 0,
            'detail_hit_rate': (detail_hits / total_count) * 100 if total_count > 0 else 0
        }
