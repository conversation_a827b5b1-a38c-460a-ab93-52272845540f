"""
文件下载器

专门负责文件的实际下载操作，包括单页面下载和动图处理
"""

import os
import zipfile
import logging
from typing import Optional, Callable, List
from pathlib import Path

from ...models.artwork import Artwork, ArtworkPage, ArtworkType
from ...models.config import DownloadConfig, GifMode

try:
    from PIL import Image
except ImportError:
    Image = None


class FileDownloader:
    """文件下载器 - 专注文件下载功能"""
    
    def __init__(self, api_service, download_config: DownloadConfig):
        """
        初始化文件下载器
        
        Args:
            api_service: API服务实例
            download_config: 下载配置
        """
        self.api_service = api_service
        self.download_config = download_config
        self.logger = logging.getLogger(__name__)
    
    def download_artwork_pages(self, artwork: Artwork, save_dir: str, 
                             stop_signal_callback: Optional[Callable] = None) -> bool:
        """
        下载作品的所有页面
        
        Args:
            artwork: 作品对象
            save_dir: 保存目录
            stop_signal_callback: 停止信号回调
            
        Returns:
            bool: 是否下载成功
        """
        try:
            if not artwork.pages:
                self.logger.warning(f"⚠️ 作品 {artwork.id} 没有页面信息")
                return False
            
            success_count = 0
            total_pages = len(artwork.pages)
            
            self.logger.info(f"📥 开始下载作品 {artwork.id} 的 {total_pages} 个页面")
            
            for page in artwork.pages:
                # 检查停止信号
                if stop_signal_callback and stop_signal_callback():
                    self.logger.info(f"🛑 收到停止信号，中断页面下载: {artwork.id}")
                    return False
                
                if self._download_single_page(artwork, page, save_dir, stop_signal_callback):
                    success_count += 1
                else:
                    self.logger.warning(f"❌ 页面下载失败: {artwork.id} - p{page.page_number}")
            
            # 处理动图
            if artwork.type == ArtworkType.UGOIRA and success_count > 0:
                if stop_signal_callback and stop_signal_callback():
                    self.logger.info(f"🛑 收到停止信号，跳过动图处理: {artwork.id}")
                    return success_count > 0
                
                self._process_ugoira(artwork, save_dir, stop_signal_callback)
            
            success_rate = success_count / total_pages if total_pages > 0 else 0
            self.logger.info(f"📊 作品 {artwork.id} 下载完成: {success_count}/{total_pages} ({success_rate:.1%})")
            
            return success_count > 0
        
        except Exception as e:
            self.logger.error(f"❌ 下载作品页面异常: {artwork.id} - {e}")
            return False
    
    def _download_single_page(self, artwork: Artwork, page: ArtworkPage, save_dir: str,
                            stop_signal_callback: Optional[Callable] = None) -> bool:
        """
        下载单个页面
        
        Args:
            artwork: 作品对象
            page: 页面对象
            save_dir: 保存目录
            stop_signal_callback: 停止信号回调
            
        Returns:
            bool: 是否下载成功
        """
        try:
            # 检查停止信号
            if stop_signal_callback and stop_signal_callback():
                self.logger.info(f"🛑 收到停止信号，跳过页面下载: {artwork.id}")
                return False
            
            # 生成文件名
            extension = page.url_original.split('.')[-1]
            if artwork.is_multi_page:
                filename = f"p{page.page_number}.{extension}"
            else:
                filename = f"p0.{extension}"
            
            file_path = os.path.join(save_dir, filename)
            
            # 如果文件已存在，跳过
            if os.path.exists(file_path):
                self.logger.debug(f"📁 文件已存在，跳过: {filename}")
                return True
            
            # 再次检查停止信号（在实际下载前）
            if stop_signal_callback and stop_signal_callback():
                self.logger.info(f"🛑 收到停止信号，取消下载: {filename}")
                return False
            
            # 下载文件
            self.logger.debug(f"📥 正在下载: {filename}")
            success = self.api_service.download_file(
                page.url_original,
                file_path,
                stop_signal_callback=stop_signal_callback
            )
            
            # 下载完成后检查停止信号
            if stop_signal_callback and stop_signal_callback():
                self.logger.info(f"🛑 下载过程中收到停止信号，清理文件: {filename}")
                # 如果收到停止信号，删除可能不完整的文件
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        self.logger.debug(f"🗑️ 已清理不完整文件: {filename}")
                except Exception:
                    pass
                return False
            
            if success and os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                size_mb = file_size / (1024 * 1024)
                self.logger.debug(f"✅ 下载成功: {filename} ({size_mb:.2f}MB)")
                return True
            else:
                self.logger.warning(f"❌ 下载失败: {filename}")
                return False
        
        except Exception as e:
            self.logger.error(f"❌ 下载页面异常: {e}")
            return False
    
    def _process_ugoira(self, artwork: Artwork, save_dir: str,
                       stop_signal_callback: Optional[Callable] = None) -> None:
        """
        处理动图（Ugoira）
        
        Args:
            artwork: 作品对象
            save_dir: 保存目录
            stop_signal_callback: 停止信号回调
        """
        try:
            self.logger.info(f"🎬 开始处理动图: {artwork.id}")
            
            # 获取动图zip文件URL和帧信息
            zip_url = artwork.ugoira_metadata.get('zip_url') if artwork.ugoira_metadata else None
            frames = artwork.ugoira_metadata.get('frames', []) if artwork.ugoira_metadata else []
            
            if not zip_url:
                self.logger.warning(f"⚠️ 动图 {artwork.id} 缺少zip文件URL")
                return
            
            # 下载zip文件
            zip_path = os.path.join(save_dir, 'animation.zip')
            if self.api_service.download_file(zip_url, zip_path):
                # 检查停止信号
                if stop_signal_callback and stop_signal_callback():
                    self.logger.info(f"🛑 收到停止信号，停止动图帧处理: {artwork.id}")
                    return
                
                # 解压帧文件
                frames_dir = os.path.join(save_dir, 'frames')
                os.makedirs(frames_dir, exist_ok=True)
                
                with zipfile.ZipFile(zip_path, 'r') as zip_file:
                    zip_file.extractall(frames_dir)
                
                # 根据配置处理GIF（检查停止信号）
                if self.download_config.gif_mode in [GifMode.GIF_ONLY, GifMode.BOTH] and not (stop_signal_callback and stop_signal_callback()):
                    self._create_gif_from_frames(frames_dir, frames, save_dir)
                elif stop_signal_callback and stop_signal_callback():
                    self.logger.info(f"🛑 收到停止信号，跳过GIF创建: {artwork.id}")
                
                # 清理zip文件（如果配置要求）
                if self.download_config.gif_mode == GifMode.GIF_ONLY:
                    try:
                        os.remove(zip_path)
                        # 也可以选择删除frames目录
                        import shutil
                        shutil.rmtree(frames_dir, ignore_errors=True)
                    except Exception:
                        pass
            
        except Exception as e:
            self.logger.error(f"❌ 处理动图异常: {artwork.id} - {e}")
    
    def _create_gif_from_frames(self, frames_dir: str, frames: List[dict], save_dir: str) -> None:
        """
        从帧文件创建GIF
        
        Args:
            frames_dir: 帧文件目录
            frames: 帧信息列表
            save_dir: 保存目录
        """
        if not Image:
            self.logger.warning("⚠️ PIL库未安装，无法创建GIF")
            return
        
        try:
            self.logger.info("🎨 开始创建GIF...")
            
            # 收集所有帧文件
            frame_files = []
            durations = []
            
            for frame in frames:
                frame_file = os.path.join(frames_dir, frame['file'])
                if os.path.exists(frame_file):
                    frame_files.append(frame_file)
                    durations.append(frame.get('delay', 100))  # 默认100ms
            
            if not frame_files:
                self.logger.warning("⚠️ 没有找到有效的帧文件")
                return
            
            # 创建GIF
            images = []
            for frame_file in frame_files:
                img = Image.open(frame_file)
                # 转换为RGB模式（GIF需要）
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                images.append(img)
            
            if images:
                gif_path = os.path.join(save_dir, 'animation.gif')
                images[0].save(
                    gif_path,
                    save_all=True,
                    append_images=images[1:],
                    duration=durations,
                    loop=0,
                    optimize=True
                )
                
                gif_size = os.path.getsize(gif_path) / (1024 * 1024)
                self.logger.info(f"✅ GIF创建成功: animation.gif ({gif_size:.2f}MB)")
            
        except Exception as e:
            self.logger.error(f"❌ 创建GIF失败: {e}")
    
    def validate_download(self, file_path: str, expected_size: Optional[int] = None) -> bool:
        """
        验证下载的文件
        
        Args:
            file_path: 文件路径
            expected_size: 期望的文件大小（字节）
            
        Returns:
            bool: 文件是否有效
        """
        try:
            if not os.path.exists(file_path):
                return False
            
            file_size = os.path.getsize(file_path)
            
            # 检查文件大小
            if file_size == 0:
                self.logger.warning(f"⚠️ 文件大小为0: {file_path}")
                return False
            
            # 如果提供了期望大小，进行比较
            if expected_size is not None and abs(file_size - expected_size) > 1024:  # 允许1KB误差
                self.logger.warning(f"⚠️ 文件大小不匹配: {file_path} (实际: {file_size}, 期望: {expected_size})")
                return False
            
            # 简单的文件头检查（针对图片文件）
            if file_path.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
                return self._validate_image_file(file_path)
            
            return True
        
        except Exception as e:
            self.logger.error(f"❌ 验证文件失败: {file_path} - {e}")
            return False
    
    def _validate_image_file(self, file_path: str) -> bool:
        """
        验证图片文件
        
        Args:
            file_path: 图片文件路径
            
        Returns:
            bool: 图片是否有效
        """
        try:
            # 检查文件头
            with open(file_path, 'rb') as f:
                header = f.read(16)
            
            # 常见图片格式的文件头
            image_signatures = {
                b'\xff\xd8\xff': 'JPEG',
                b'\x89PNG\r\n\x1a\n': 'PNG',
                b'GIF87a': 'GIF',
                b'GIF89a': 'GIF',
                b'RIFF': 'WEBP'  # WEBP文件以RIFF开头
            }
            
            for signature, format_name in image_signatures.items():
                if header.startswith(signature):
                    return True
            
            self.logger.warning(f"⚠️ 未识别的图片格式: {file_path}")
            return False
        
        except Exception as e:
            self.logger.error(f"❌ 验证图片文件失败: {file_path} - {e}")
            return False
