# 画师作品URL生成功能

## 概述

本文档描述了Pixiv Spider v6.0中新增的画师作品URL生成功能。该功能允许系统根据画师ID和页码自动生成正确的Pixiv画师作品页面URL。

## URL格式

画师作品URL遵循以下格式：

```
默认URL：https://www.pixiv.net/users/画师ID/artworks?p=页码数
```

### 具体规则

- **第1页**：`https://www.pixiv.net/users/{画师ID}/artworks`（不包含页码参数）
- **第2页及以后**：`https://www.pixiv.net/users/{画师ID}/artworks?p={页码}`

### 示例

- 画师ID 123456 的第1页：`https://www.pixiv.net/users/123456/artworks`
- 画师ID 123456 的第2页：`https://www.pixiv.net/users/123456/artworks?p=2`
- 画师ID 123456 的第5页：`https://www.pixiv.net/users/123456/artworks?p=5`

## 后端实现

### DownloadConfig类

在 `src/pixiv_spider/models/config.py` 中的 `DownloadConfig` 类新增了 `get_user_artworks_url` 方法：

```python
def get_user_artworks_url(self, page: int = 1) -> str:
    """
    构建画师作品URL
    
    Args:
        page: 页码
        
    Returns:
        str: 完整的画师作品URL
        
    Raises:
        ValueError: 当user_id未设置时抛出异常
    """
    if not self.user_id:
        raise ValueError("画师作品模式下必须设置user_id")
    
    # 构建基础URL
    base_url = f"https://www.pixiv.net/users/{self.user_id}/artworks"
    
    # 添加页码参数（只有大于1时才添加）
    if page > 1:
        return f"{base_url}?p={page}"
    else:
        return base_url
```

### 使用方法

```python
from pixiv_spider.models.config import DownloadConfig, DownloadMode

# 创建配置
config = DownloadConfig()
config.download_mode = DownloadMode.USER
config.user_id = 123456

# 生成URL
url_page1 = config.get_user_artworks_url(1)  # 第1页
url_page2 = config.get_user_artworks_url(2)  # 第2页
```

### Selenium集成

`selenium_utils.py` 中的 `get_user_artworks` 方法已更新，支持使用配置对象生成URL：

```python
def get_user_artworks(self, user_id: int, pages: int = 5, download_config=None) -> Set[str]:
    """
    获取用户作品链接
    
    Args:
        user_id: 用户ID
        pages: 页数
        download_config: 下载配置对象（可选，用于生成URL）
        
    Returns:
        Set[str]: 作品链接集合
    """
```

## 前端实现

### UnifiedConfigService

在 `electron-gui/src/renderer/services/unified-config.js` 中更新了URL生成逻辑：

```javascript
/**
 * 生成预览URL
 */
generatePreviewUrl(mode, settings) {
  switch (mode) {
    case 'artist':
      const artistId = settings?.artistId || ''
      if (!artistId) return ''
      
      // 生成画师作品URL，支持页码参数
      const page = settings?.pageStart || 1
      const baseUrl = `https://www.pixiv.net/users/${artistId}/artworks`
      return page > 1 ? `${baseUrl}?p=${page}` : baseUrl
      
    // ... 其他模式
  }
}

/**
 * 生成画师作品URL（支持指定页码）
 */
generateArtistUrl(artistId, page = 1) {
  if (!artistId) {
    throw new Error('画师作品模式下必须提供画师ID')
  }
  
  const baseUrl = `https://www.pixiv.net/users/${artistId}/artworks`
  return page > 1 ? `${baseUrl}?p=${page}` : baseUrl
}
```

## 错误处理

### 后端错误处理

- 当 `user_id` 未设置时，`get_user_artworks_url` 方法会抛出 `ValueError` 异常
- 异常信息：`"画师作品模式下必须设置user_id"`

### 前端错误处理

- 当 `artistId` 未提供时，`generateArtistUrl` 方法会抛出错误
- 错误信息：`"画师作品模式下必须提供画师ID"`

## 测试

功能已通过完整的单元测试验证，包括：

1. ✅ 第1页URL生成（不包含页码参数）
2. ✅ 第2页及以后URL生成（包含页码参数）
3. ✅ 不同画师ID的URL生成
4. ✅ 错误情况处理（未设置画师ID）

## 兼容性

- 该功能向后兼容，不会影响现有代码
- 如果没有提供配置对象，系统会回退到原有的硬编码URL生成方式
- 前端和后端都支持新的URL生成逻辑

## 注意事项

1. 画师ID必须是有效的数字
2. 页码从1开始计数
3. 第1页URL不包含页码参数，符合Pixiv网站的标准格式
4. 该功能仅适用于画师作品模式（`DownloadMode.USER`）

## 更新日志

- **v6.0**: 新增画师作品URL生成功能
- 支持动态页码参数
- 前后端统一URL生成逻辑
- 完整的错误处理和测试覆盖
