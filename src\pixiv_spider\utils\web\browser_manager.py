"""
浏览器进程管理器

管理Chrome浏览器进程的生命周期和资源清理
"""

import logging
import psutil
import os
import time
from typing import Optional, List
from selenium import webdriver
from selenium.webdriver.chrome.service import Service

from .selenium_config import SeleniumConfig, ChromeOptionsBuilder


class BrowserManager:
    """浏览器进程管理器"""
    
    def __init__(self, config: Optional[SeleniumConfig] = None):
        """
        初始化浏览器管理器
        
        Args:
            config: Selenium配置
        """
        self.logger = logging.getLogger(__name__)
        self.config = config or SeleniumConfig.create_default()
        
        # 进程跟踪
        self._driver_pid: Optional[int] = None
        self._browser_pid: Optional[int] = None
        self._driver: Optional[webdriver.Chrome] = None
    
    def create_driver(self) -> webdriver.Chrome:
        """
        创建Chrome驱动器
        
        Returns:
            webdriver.Chrome: Chrome驱动器实例
        """
        try:
            # 构建Chrome选项
            options = ChromeOptionsBuilder.build_options(self.config)
            
            # 创建服务
            service = Service()
            
            # 创建驱动器
            driver = webdriver.Chrome(service=service, options=options)
            
            # 设置超时
            driver.set_page_load_timeout(self.config.page_load_timeout)
            driver.implicitly_wait(self.config.implicit_wait)
            
            # 记录进程ID
            self._driver = driver
            self._driver_pid = driver.service.process.pid if driver.service.process else None
            
            # 获取浏览器进程ID
            self._browser_pid = self._find_browser_process()
            
            # 隐藏自动化特征
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.logger.info(f"✅ Chrome驱动器创建成功 (PID: {self._driver_pid})")
            return driver
            
        except Exception as e:
            self.logger.error(f"❌ 创建Chrome驱动器失败: {e}")
            raise
    
    def _find_browser_process(self) -> Optional[int]:
        """查找Chrome浏览器进程"""
        try:
            if not self._driver_pid:
                return None
                
            # 查找ChromeDriver的子进程
            for proc in psutil.process_iter(['pid', 'ppid', 'name']):
                try:
                    if proc.info['ppid'] == self._driver_pid and 'chrome' in proc.info['name'].lower():
                        return proc.info['pid']
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
        except Exception as e:
            self.logger.debug(f"查找浏览器进程失败: {e}")
            
        return None
    
    def cleanup_driver(self) -> None:
        """清理驱动器和相关进程"""
        try:
            # 关闭驱动器
            if self._driver:
                try:
                    self._driver.quit()
                    self.logger.info("✅ Chrome驱动器已关闭")
                except Exception as e:
                    self.logger.warning(f"⚠️ 关闭驱动器时出现警告: {e}")
                finally:
                    self._driver = None
            
            # 清理进程
            self._cleanup_processes()
            
        except Exception as e:
            self.logger.error(f"❌ 清理驱动器失败: {e}")
    
    def _cleanup_processes(self) -> None:
        """清理相关进程"""
        cleaned_count = 0
        
        # 清理记录的进程
        for pid, name in [(self._browser_pid, "Chrome浏览器"), (self._driver_pid, "ChromeDriver")]:
            if pid and self._kill_process(pid):
                self.logger.info(f"✅ 已清理{name}进程 (PID: {pid})")
                cleaned_count += 1
        
        # 清理孤儿Chrome进程
        orphan_count = self._cleanup_orphan_chrome_processes()
        
        if cleaned_count > 0 or orphan_count > 0:
            self.logger.info(f"🧹 进程清理完成: 主进程{cleaned_count}个, 孤儿进程{orphan_count}个")
        
        # 重置进程ID
        self._driver_pid = None
        self._browser_pid = None
    
    def _kill_process(self, pid: int) -> bool:
        """终止指定进程"""
        try:
            if psutil.pid_exists(pid):
                process = psutil.Process(pid)
                process.terminate()
                
                # 等待进程终止
                try:
                    process.wait(timeout=3)
                except psutil.TimeoutExpired:
                    # 强制杀死
                    process.kill()
                    process.wait(timeout=2)
                
                return True
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
            pass
        except Exception as e:
            self.logger.debug(f"终止进程 {pid} 失败: {e}")
        
        return False
    
    def _cleanup_orphan_chrome_processes(self) -> int:
        """清理孤儿Chrome进程"""
        cleaned_count = 0
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    if not proc_info['name'] or 'chrome' not in proc_info['name'].lower():
                        continue
                    
                    # 检查是否为测试相关的Chrome进程
                    cmdline = proc_info.get('cmdline', [])
                    if any('--test-type' in arg or '--remote-debugging-port' in arg for arg in cmdline):
                        if self._kill_process(proc_info['pid']):
                            cleaned_count += 1
                            
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
        except Exception as e:
            self.logger.debug(f"清理孤儿进程失败: {e}")
        
        return cleaned_count
    
    def is_driver_alive(self) -> bool:
        """检查驱动器是否仍然活跃"""
        if not self._driver:
            return False
        
        try:
            # 尝试获取当前URL
            _ = self._driver.current_url
            return True
        except Exception:
            return False
    
    def get_process_info(self) -> dict:
        """获取进程信息"""
        return {
            'driver_pid': self._driver_pid,
            'browser_pid': self._browser_pid,
            'driver_alive': self.is_driver_alive(),
            'driver_exists': self._driver is not None
        }
    
    def __del__(self):
        """析构函数"""
        try:
            self.cleanup_driver()
        except Exception:
            pass
