"""
路径处理工具

提供路径相关的工具函数
"""

import os
import re
from pathlib import Path
from typing import Optional, List
import logging


class PathUtils:
    """路径处理工具类"""
    
    @staticmethod
    def safe_filename(filename: str, max_length: int = 255) -> str:
        """
        生成安全的文件名
        
        Args:
            filename: 原始文件名
            max_length: 最大长度
            
        Returns:
            str: 安全的文件名
        """
        # 替换非法字符
        illegal_chars = '<>:"/\\|?*'
        safe_name = filename
        
        for char in illegal_chars:
            safe_name = safe_name.replace(char, '_')
        
        # 去除首尾空格和点
        safe_name = safe_name.strip(' .')
        
        # 限制长度
        if len(safe_name) > max_length:
            name, ext = os.path.splitext(safe_name)
            max_name_length = max_length - len(ext)
            safe_name = name[:max_name_length] + ext
        
        # 如果名字为空，使用默认名称
        if not safe_name:
            safe_name = "unnamed"
        
        return safe_name
    
    @staticmethod
    def safe_path(path: str) -> str:
        """
        生成安全的路径
        
        Args:
            path: 原始路径
            
        Returns:
            str: 安全的路径
        """
        # 分割路径
        parts = Path(path).parts
        
        # 处理每个部分
        safe_parts = []
        for part in parts:
            if part in ('/', '\\'):  # 根目录
                safe_parts.append(part)
            else:
                safe_parts.append(PathUtils.safe_filename(part))
        
        return os.path.join(*safe_parts) if safe_parts else ""
    
    @staticmethod
    def normalize_path(path: str) -> str:
        """
        标准化路径
        
        Args:
            path: 原始路径
            
        Returns:
            str: 标准化后的路径
        """
        return os.path.normpath(os.path.abspath(path))
    
    @staticmethod
    def get_relative_path(path: str, base_path: str) -> str:
        """
        获取相对路径
        
        Args:
            path: 目标路径
            base_path: 基础路径
            
        Returns:
            str: 相对路径
        """
        try:
            return os.path.relpath(path, base_path)
        except ValueError:
            # 不同驱动器的情况
            return path
    
    @staticmethod
    def join_paths(*paths: str) -> str:
        """
        安全地连接路径
        
        Args:
            *paths: 路径组件
            
        Returns:
            str: 连接后的路径
        """
        return os.path.join(*paths)
    
    @staticmethod
    def split_path(path: str) -> tuple:
        """
        分割路径为目录和文件名
        
        Args:
            path: 文件路径
            
        Returns:
            tuple: (目录, 文件名)
        """
        return os.path.split(path)
    
    @staticmethod
    def split_extension(path: str) -> tuple:
        """
        分割文件名和扩展名
        
        Args:
            path: 文件路径
            
        Returns:
            tuple: (文件名, 扩展名)
        """
        return os.path.splitext(path)
    
    @staticmethod
    def get_filename(path: str) -> str:
        """
        获取文件名（不含路径）
        
        Args:
            path: 文件路径
            
        Returns:
            str: 文件名
        """
        return os.path.basename(path)
    
    @staticmethod
    def get_directory(path: str) -> str:
        """
        获取目录路径
        
        Args:
            path: 文件路径
            
        Returns:
            str: 目录路径
        """
        return os.path.dirname(path)
    
    @staticmethod
    def get_extension(path: str) -> str:
        """
        获取文件扩展名
        
        Args:
            path: 文件路径
            
        Returns:
            str: 扩展名（包含点号）
        """
        return os.path.splitext(path)[1]
    
    @staticmethod
    def change_extension(path: str, new_ext: str) -> str:
        """
        更改文件扩展名
        
        Args:
            path: 原始路径
            new_ext: 新扩展名（可以包含或不包含点号）
            
        Returns:
            str: 新路径
        """
        name, _ = os.path.splitext(path)
        if not new_ext.startswith('.'):
            new_ext = '.' + new_ext
        return name + new_ext
    
    @staticmethod
    def ensure_extension(path: str, ext: str) -> str:
        """
        确保路径有指定的扩展名
        
        Args:
            path: 文件路径
            ext: 期望的扩展名
            
        Returns:
            str: 带扩展名的路径
        """
        if not ext.startswith('.'):
            ext = '.' + ext
        
        if not path.lower().endswith(ext.lower()):
            return path + ext
        return path
    
    @staticmethod
    def is_absolute(path: str) -> bool:
        """
        检查是否为绝对路径
        
        Args:
            path: 路径
            
        Returns:
            bool: 是否为绝对路径
        """
        return os.path.isabs(path)
    
    @staticmethod
    def exists(path: str) -> bool:
        """
        检查路径是否存在
        
        Args:
            path: 路径
            
        Returns:
            bool: 是否存在
        """
        return os.path.exists(path)
    
    @staticmethod
    def is_file(path: str) -> bool:
        """
        检查是否为文件
        
        Args:
            path: 路径
            
        Returns:
            bool: 是否为文件
        """
        return os.path.isfile(path)
    
    @staticmethod
    def is_directory(path: str) -> bool:
        """
        检查是否为目录
        
        Args:
            path: 路径
            
        Returns:
            bool: 是否为目录
        """
        return os.path.isdir(path)
    
    @staticmethod
    def find_files(directory: str, pattern: str = "*", recursive: bool = False) -> List[str]:
        """
        查找文件
        
        Args:
            directory: 搜索目录
            pattern: 文件模式
            recursive: 是否递归搜索
            
        Returns:
            List[str]: 找到的文件列表
        """
        try:
            path_obj = Path(directory)
            if recursive:
                return [str(p) for p in path_obj.rglob(pattern) if p.is_file()]
            else:
                return [str(p) for p in path_obj.glob(pattern) if p.is_file()]
        except Exception as e:
            logging.error(f"查找文件失败: {directory}, 错误: {e}")
            return []
