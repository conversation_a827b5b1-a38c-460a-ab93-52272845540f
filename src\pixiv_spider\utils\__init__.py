"""
工具模块 - 重构后的模块化架构

包含各种实用工具函数和类，现在采用模块化设计
"""

# 保持向后兼容的导入
from .selenium_utils import SeleniumDriver as OldSeleniumDriver
from .file_utils import FileUtils
from .unified_validator import UnifiedValidator
from .cache_manager import CacheManager
from .unified_error_handler import UnifiedErrorHandler

# 新的模块化工具
from .web import SeleniumDriver, SeleniumConfig, PageExtractor, BrowserManager
from .io import FileOperations, PathUtils, DirectoryManager
from .monitoring import MemoryMonitor, ResourceMonitor, PerformanceTracker
from .logging import LogFormatter, StandardLogger, get_standard_logger

__all__ = [
    # 向后兼容的工具类
    "OldSeleniumDriver",
    "FileUtils",
    "UnifiedValidator",
    "CacheManager",
    "UnifiedErrorHandler",

    # 新的模块化工具
    "SeleniumDriver",
    "SeleniumConfig",
    "PageExtractor",
    "BrowserManager",
    "FileOperations",
    "PathUtils",
    "DirectoryManager",
    "MemoryMonitor",
    "ResourceMonitor",
    "PerformanceTracker",
    "LogFormatter",
    "StandardLogger",
    "get_standard_logger"
]