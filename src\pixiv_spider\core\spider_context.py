"""
爬虫上下文管理器 - 重构后使用简化的依赖注入

负责管理爬虫的整体上下文，包括服务容器、配置和状态
"""

import logging
from typing import Optional, Dict, Any

from ..container.service_container import ServiceContainer
from ..container.service_registry import ServiceRegistry, ServiceBuilder
from ..container.service_factory import ServiceProvider
from ..config.config_manager import ConfigManager
from ..models.config import DownloadConfig, SpiderConfig
from ..interfaces.auth_interface import IAuthService
from ..interfaces.api_interface import IApiService
from ..interfaces.download_interface import IDownloadService


class SpiderContext:
    """爬虫上下文管理器 - 使用简化的依赖注入"""

    def __init__(self, container_or_config=None):
        """
        初始化爬虫上下文

        Args:
            container_or_config: 服务容器或配置管理器
        """
        self.logger = logging.getLogger(__name__)

        # 处理不同类型的输入参数
        if isinstance(container_or_config, ConfigManager):
            self.config_manager = container_or_config
            self.container = (ServiceBuilder()
                            .with_custom_config_manager(self.config_manager)
                            .with_default_services()
                            .build())
        elif isinstance(container_or_config, ServiceContainer):
            self.container = container_or_config
            self.config_manager = self.container.get_config_manager()
        else:
            self.container = (ServiceBuilder()
                            .with_default_services()
                            .build())
            self.config_manager = self.container.get_config_manager()

        # 服务提供者
        self.service_provider = ServiceProvider(self.container)

        # 加载配置
        self.download_config = self.config_manager.load_download_config()
        self.spider_config = self.config_manager.load_spider_config()

        # 核心服务引用
        self.auth_service: Optional[IAuthService] = None
        self.api_service: Optional[IApiService] = None
        self.download_service: Optional[IDownloadService] = None

    def ensure_services_initialized(self) -> None:
        """确保基础服务已初始化"""
        if self.auth_service is None:
            self.auth_service = self.service_provider.get_auth_service()
            self.logger.debug("✅ 认证服务已初始化")

    def setup_services_with_cookies(self, cookies: Dict[str, Any]) -> None:
        """使用cookies设置服务"""
        try:
            self.logger.info("🔧 使用cookies设置服务...")

            # 使用服务提供者设置服务
            self.service_provider.setup_with_cookies(cookies)

            # 更新服务引用
            self.api_service = self.service_provider.get_api_service()
            self.download_service = self.service_provider.get_download_service()

            self.logger.info("✅ 服务设置完成")

        except Exception as e:
            self.logger.error(f"❌ 设置服务失败: {e}")
            raise

    def update_download_config(self, config: DownloadConfig) -> None:
        """更新下载配置"""
        self.download_config = config
        self.config_manager.save_download_config(config)

        # 更新下载服务的配置
        if self.download_service:
            self.download_service.download_config = config

        self.logger.info("✅ 下载配置已更新")

    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            'container_services': self.container.get_registered_services(),
            'provider_status': self.service_provider.get_service_status(),
            'active_services': {
                'auth_service': self.auth_service is not None,
                'api_service': self.api_service is not None,
                'download_service': self.download_service is not None
            }
        }

    def cleanup(self) -> None:
        """清理上下文资源"""
        try:
            # 清理服务提供者缓存
            self.service_provider.clear_cache()

            # 清理作用域服务
            self.container.clear_scoped()

            # 重置服务引用
            self.auth_service = None
            self.api_service = None
            self.download_service = None

            self.logger.info("✅ 爬虫上下文资源已清理")

        except Exception as e:
            self.logger.error(f"❌ 清理爬虫上下文失败: {e}")
    
    def update_spider_config(self, config: SpiderConfig) -> None:
        """更新爬虫配置"""
        self.spider_config = config
        self.config_manager.save_spider_config(config)
        self.logger.info("爬虫配置已更新")
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            'services_registered': self._services_registered,
            'auth_service_available': self.auth_service is not None,
            'api_service_available': self.api_service is not None,
            'download_service_available': self.download_service is not None,
            'download_mode': self.download_config.download_mode.value,
            'save_path': self.download_config.save_path
        }
