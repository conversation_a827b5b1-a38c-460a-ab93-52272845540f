"""
简化的服务容器

实现轻量级依赖注入和服务管理
"""

import logging
from typing import Dict, Any, Type, TypeVar, Optional, Callable, Union
from enum import Enum
from ..config.config_manager import ConfigManager

T = TypeVar('T')


class ServiceLifetime(Enum):
    """服务生命周期"""
    SINGLETON = "singleton"  # 单例
    TRANSIENT = "transient"  # 瞬态
    SCOPED = "scoped"       # 作用域


class ServiceDescriptor:
    """服务描述符"""

    def __init__(self,
                 service_type: Type[T],
                 implementation: Union[Type[T], Callable, Any],
                 lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT):
        self.service_type = service_type
        self.implementation = implementation
        self.lifetime = lifetime


class ServiceContainer:
    """简化的服务容器"""

    def __init__(self):
        """初始化服务容器"""
        self._descriptors: Dict[str, ServiceDescriptor] = {}
        self._singletons: Dict[str, Any] = {}
        self._scoped_instances: Dict[str, Any] = {}
        self.logger = logging.getLogger(__name__)

        # 注册配置管理器作为默认单例服务
        self._register_default_services()

    def _register_default_services(self) -> None:
        """注册默认服务"""
        config_manager = ConfigManager()
        self._singletons['config_manager'] = config_manager
        self.logger.debug("默认服务注册完成")

    def register(self,
                service_type: Type[T],
                implementation: Union[Type[T], Callable, Any] = None,
                lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT,
                name: Optional[str] = None) -> 'ServiceContainer':
        """
        注册服务

        Args:
            service_type: 服务类型
            implementation: 实现类型、工厂函数或实例
            lifetime: 生命周期
            name: 服务名称（可选）

        Returns:
            ServiceContainer: 支持链式调用
        """
        service_name = name or service_type.__name__

        if implementation is None:
            implementation = service_type

        descriptor = ServiceDescriptor(service_type, implementation, lifetime)
        self._descriptors[service_name] = descriptor

        self.logger.debug(f"注册服务: {service_name} ({lifetime.value})")
        return self

    def register_singleton(self,
                          service_type: Type[T],
                          implementation: Union[Type[T], Callable, Any] = None,
                          name: Optional[str] = None) -> 'ServiceContainer':
        """注册单例服务"""
        return self.register(service_type, implementation, ServiceLifetime.SINGLETON, name)

    def register_transient(self,
                          service_type: Type[T],
                          implementation: Union[Type[T], Callable, Any] = None,
                          name: Optional[str] = None) -> 'ServiceContainer':
        """注册瞬态服务"""
        return self.register(service_type, implementation, ServiceLifetime.TRANSIENT, name)

    def register_scoped(self,
                       service_type: Type[T],
                       implementation: Union[Type[T], Callable, Any] = None,
                       name: Optional[str] = None) -> 'ServiceContainer':
        """注册作用域服务"""
        return self.register(service_type, implementation, ServiceLifetime.SCOPED, name)

    def register_instance(self, service_type: Type[T], instance: T, name: Optional[str] = None) -> 'ServiceContainer':
        """
        注册服务实例

        Args:
            service_type: 服务类型
            instance: 服务实例
            name: 服务名称（可选）

        Returns:
            ServiceContainer: 支持链式调用
        """
        service_name = name or service_type.__name__
        self._singletons[service_name] = instance
        self.logger.debug(f"注册服务实例: {service_name}")
        return self

    def get(self, service_type: Union[Type[T], str]) -> T:
        """
        获取服务实例

        Args:
            service_type: 服务类型或名称

        Returns:
            T: 服务实例
        """
        service_name = service_type if isinstance(service_type, str) else service_type.__name__

        # 检查已注册的单例实例
        if service_name in self._singletons:
            return self._singletons[service_name]

        # 检查服务描述符
        if service_name not in self._descriptors:
            raise ValueError(f"服务未注册: {service_name}")

        descriptor = self._descriptors[service_name]

        # 根据生命周期创建实例
        if descriptor.lifetime == ServiceLifetime.SINGLETON:
            if service_name not in self._singletons:
                instance = self._create_instance(descriptor)
                self._singletons[service_name] = instance
            return self._singletons[service_name]

        elif descriptor.lifetime == ServiceLifetime.SCOPED:
            if service_name not in self._scoped_instances:
                instance = self._create_instance(descriptor)
                self._scoped_instances[service_name] = instance
            return self._scoped_instances[service_name]

        else:  # TRANSIENT
            return self._create_instance(descriptor)

    def get_config_manager(self) -> ConfigManager:
        """获取配置管理器"""
        return self._singletons['config_manager']

    def try_get(self, service_type: Union[Type[T], str]) -> Optional[T]:
        """
        尝试获取服务实例，失败时返回None

        Args:
            service_type: 服务类型或名称

        Returns:
            Optional[T]: 服务实例或None
        """
        try:
            return self.get(service_type)
        except ValueError:
            return None

    def _create_instance(self, descriptor: ServiceDescriptor) -> Any:
        """
        创建服务实例

        Args:
            descriptor: 服务描述符

        Returns:
            Any: 服务实例
        """
        implementation = descriptor.implementation

        try:
            # 如果是可调用对象（工厂函数）
            if callable(implementation) and not isinstance(implementation, type):
                return implementation(self)

            # 如果是类型，尝试构造实例
            if isinstance(implementation, type):
                return self._construct_instance(implementation)

            # 如果是实例，直接返回
            return implementation

        except Exception as e:
            self.logger.error(f"创建服务实例失败: {descriptor.service_type.__name__}, 错误: {e}")
            raise

    def _construct_instance(self, service_class: Type[T]) -> T:
        """
        构造服务实例

        Args:
            service_class: 服务类

        Returns:
            T: 服务实例
        """
        try:
            # 尝试使用配置管理器创建实例
            config_manager = self.get_config_manager()
            return service_class(config_manager)
        except TypeError:
            try:
                # 尝试无参数创建
                return service_class()
            except Exception as e:
                self.logger.error(f"构造服务实例失败: {service_class.__name__}, 错误: {e}")
                raise

    def has(self, service_type: Union[Type[T], str]) -> bool:
        """
        检查服务是否已注册

        Args:
            service_type: 服务类型或名称

        Returns:
            bool: 是否已注册
        """
        service_name = service_type if isinstance(service_type, str) else service_type.__name__
        return service_name in self._descriptors or service_name in self._singletons

    def clear_scoped(self) -> None:
        """清空作用域服务实例"""
        self._scoped_instances.clear()
        self.logger.debug("作用域服务实例已清空")

    def clear(self) -> None:
        """清空所有服务"""
        self._descriptors.clear()
        self._singletons.clear()
        self._scoped_instances.clear()
        self.logger.info("服务容器已清空")

    def get_registered_services(self) -> Dict[str, str]:
        """获取已注册的服务列表"""
        services = {}

        # 添加描述符中的服务
        for name, descriptor in self._descriptors.items():
            services[name] = descriptor.lifetime.value

        # 添加单例实例
        for name in self._singletons.keys():
            if name not in services:
                services[name] = "instance"

        return services
