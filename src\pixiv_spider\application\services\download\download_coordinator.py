"""
下载协调器

负责协调各个下载组件，实现完整的下载流程
"""

import os
import logging
import time
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Optional, Callable, Any

from ....interfaces.download_interface import IDownloadService
from ....models.artwork import Artwork, ArtworkStatus
from ....models.config import DownloadConfig
from ....config.config_manager import ConfigManager

from .download_cache import DownloadCache
from .download_state_manager import DownloadStateManager
from .artwork_processor import ArtworkProcessor
from .download_validator import DownloadValidator
from ....infrastructure.network.file_downloader import FileDownloader


class DownloadCoordinator(IDownloadService):
    """下载协调器 - 整合所有下载组件"""
    
    def __init__(self, api_service, config_manager: Optional[ConfigManager] = None):
        """
        初始化下载协调器
        
        Args:
            api_service: API服务实例
            config_manager: 配置管理器
        """
        self.api_service = api_service
        self.config_manager = config_manager or ConfigManager()
        self.download_config = self.config_manager.load_download_config()
        self.spider_config = self.config_manager.load_spider_config()
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.cache = DownloadCache(self.download_config)
        self.state_manager = DownloadStateManager()
        self.processor = ArtworkProcessor(self.download_config)
        self.validator = DownloadValidator(self.download_config)
        self.file_downloader = FileDownloader(api_service, self.download_config)
        
        # 线程池 - 延迟初始化
        self.executor: Optional[ThreadPoolExecutor] = None
        self._max_workers = self.spider_config.concurrent_downloads
        
        # Future对象跟踪
        self._active_futures: List = []
    
    def set_progress_callback(self, callback: Callable) -> None:
        """设置进度回调函数"""
        self.state_manager.set_progress_callback(callback)
    
    def set_status_callback(self, callback: Callable) -> None:
        """设置状态回调函数"""
        self.state_manager.set_status_callback(callback)
    
    def get_download_state(self) -> Dict[str, Any]:
        """获取当前下载状态"""
        return self.state_manager.get_state()
    
    def is_downloaded(self, artwork_id: str) -> bool:
        """检查作品是否已下载"""
        return self.cache.is_downloaded(artwork_id)
    
    def get_downloaded_path(self, artwork_id: str) -> Optional[str]:
        """获取已下载作品的路径"""
        return self.cache.get_downloaded_path(artwork_id)
    
    def refresh_downloaded_cache(self) -> None:
        """刷新已下载作品缓存"""
        self.cache.refresh()
    
    def pause_download(self) -> None:
        """暂停下载"""
        self.state_manager.pause_download()
    
    def resume_download(self) -> None:
        """恢复下载"""
        self.state_manager.resume_download()
    
    def stop_download(self) -> None:
        """停止下载"""
        self.state_manager.stop_download()
    
    def reset_state(self) -> None:
        """重置下载服务状态"""
        self.logger.info("🔄 重置下载协调器状态...")
        
        # 重置状态管理器
        self.state_manager.reset_state()
        
        # 清理活跃的Future任务
        if self._active_futures:
            self.logger.info(f"🧹 清理 {len(self._active_futures)} 个残留的Future任务")
            for future in self._active_futures[:]:
                try:
                    if not future.done():
                        future.cancel()
                except Exception:
                    pass
            self._active_futures.clear()
        
        # 重新创建线程池
        if self.executor and (not hasattr(self.executor, '_shutdown') or self.executor._shutdown):
            self.logger.info("🔧 重新创建线程池...")
            self.executor = ThreadPoolExecutor(max_workers=self._max_workers)
        
        self.logger.info("✅ 下载协调器状态已重置")
    
    def cleanup_resources(self) -> None:
        """清理资源"""
        try:
            self.logger.info("🧹 开始清理下载协调器资源...")
            
            # 停止下载
            self.state_manager.stop_download()
            
            # 等待所有任务完成
            if self._active_futures:
                self.logger.info(f"⏳ 等待 {len(self._active_futures)} 个任务完成...")
                for future in self._active_futures:
                    try:
                        future.result(timeout=5)  # 最多等待5秒
                    except Exception:
                        pass
            
            # 关闭线程池
            if self.executor:
                self.executor.shutdown(wait=True)
                self.executor = None
                self.logger.info("✅ 线程池已关闭")
            
            self.logger.info("✅ 下载协调器资源清理完成")
        
        except Exception as e:
            self.logger.error(f"❌ 清理资源失败: {e}")
    
    def batch_download(self, artworks: List[Artwork]) -> Dict[str, int]:
        """
        批量下载作品
        
        Args:
            artworks: 作品列表
            
        Returns:
            Dict[str, int]: 下载统计信息
        """
        if not artworks:
            self.logger.warning("⚠️ 作品列表为空")
            return {'total': 0, 'completed': 0, 'failed': 0, 'skipped': 0}
        
        self.logger.info(f"🚀 开始批量下载 {len(artworks)} 个作品")
        
        # 初始化统计
        stats = {'total': len(artworks), 'completed': 0, 'failed': 0, 'skipped': 0}
        
        try:
            # 启动下载状态管理
            self.state_manager.start_download(len(artworks))
            
            # 预检查已下载的作品
            if self.download_config.skip_existing:
                artworks_to_download = self._precheck_downloaded_artworks(artworks, stats)
            else:
                artworks_to_download = artworks
            
            if not artworks_to_download:
                self.logger.info("📊 所有作品都已下载，无需重新下载")
                self.state_manager.complete_download()
                return stats
            
            # 确保线程池已初始化
            self._ensure_executor_initialized()
            
            # 提交下载任务
            futures = self._submit_download_tasks(artworks_to_download)
            
            # 处理下载结果
            self._process_download_results(futures, stats)
            
            # 完成下载
            self.state_manager.complete_download()
            
            # 输出最终统计
            self._log_final_stats(stats)
            
            return stats
        
        except Exception as e:
            self.logger.error(f"❌ 批量下载失败: {e}")
            self.state_manager.update_state(status='stopped')
            return stats
    
    def download_artwork(self, artwork: Artwork) -> bool:
        """
        下载单个作品
        
        Args:
            artwork: 作品对象
            
        Returns:
            bool: 是否下载成功
        """
        try:
            # 检查停止信号
            if self.state_manager.should_stop():
                artwork.status = ArtworkStatus.FAILED
                self.logger.info(f"🛑 收到停止信号，跳过作品 {artwork.id}")
                return False
            
            # 如果已经标记为跳过，直接返回
            if artwork.status == ArtworkStatus.SKIPPED:
                self.logger.info(f"⏭️ 作品 {artwork.id} 已标记为跳过")
                return True
            
            # 开始下载日志
            self.logger.info(f"🎨 开始下载作品 {artwork.id} - {artwork.title}")
            
            # 生成保存路径
            save_path = self.processor.generate_save_path(artwork)
            
            # 创建目录
            os.makedirs(save_path, exist_ok=True)
            
            # 更新状态
            artwork.status = ArtworkStatus.DOWNLOADING
            artwork.local_path = save_path
            self.state_manager.set_current_artwork(artwork.id)
            
            # 获取页面信息
            if not artwork.pages:
                if self.state_manager.should_stop():
                    artwork.status = ArtworkStatus.FAILED
                    return False
                
                pages = self.api_service.get_artwork_pages(artwork.id)
                if not pages:
                    self.logger.error(f"❌ 无法获取作品页面信息: {artwork.id}")
                    artwork.status = ArtworkStatus.FAILED
                    return False
                artwork.pages = pages
            
            # 下载文件
            download_success = self.file_downloader.download_artwork_pages(
                artwork, save_path, 
                stop_signal_callback=lambda: self.state_manager.should_stop()
            )
            
            if not download_success:
                artwork.status = ArtworkStatus.FAILED
                self.processor.cleanup_incomplete_download(save_path)
                return False
            
            # 保存元数据
            if self.download_config.save_metadata:
                self.processor.save_artwork_metadata(artwork, save_path)
            
            # 创建摘要文件
            if self.download_config.create_summary:
                self.processor.create_artwork_summary(artwork, save_path)
            
            # 验证下载
            is_valid, message = self.validator.validate_artwork_download(artwork, save_path)
            if not is_valid:
                self.logger.warning(f"⚠️ 下载验证失败: {artwork.id} - {message}")
                artwork.status = ArtworkStatus.FAILED
                return False
            
            # 更新缓存
            self.cache.add_to_cache(artwork.id, save_path)
            
            # 标记完成
            artwork.status = ArtworkStatus.COMPLETED
            artwork.download_time = time.time()
            
            self.logger.info(f"✅ 作品下载完成: {artwork.id}")
            return True
        
        except Exception as e:
            self.logger.error(f"❌ 下载作品异常: {artwork.id} - {e}")
            artwork.status = ArtworkStatus.FAILED
            return False
    
    def _ensure_executor_initialized(self) -> None:
        """确保线程池已初始化"""
        if self.executor is None:
            self.logger.info(f"🔧 初始化线程池，工作线程数: {self._max_workers}")
            self.executor = ThreadPoolExecutor(max_workers=self._max_workers)
    
    def _precheck_downloaded_artworks(self, artworks: List[Artwork], stats: Dict[str, int]) -> List[Artwork]:
        """预检查已下载的作品"""
        self.logger.info("🔍 批量检查已下载作品...")
        start_time = time.time()
        
        skipped_count = 0
        for artwork in artworks:
            if self.state_manager.should_stop():
                break
            
            if self.cache.is_downloaded(artwork.id):
                artwork.status = ArtworkStatus.SKIPPED
                artwork.local_path = self.cache.get_downloaded_path(artwork.id)
                skipped_count += 1
        
        # 过滤掉已下载的作品
        artworks_to_download = [a for a in artworks if a.status != ArtworkStatus.SKIPPED]
        check_time = time.time() - start_time
        
        self.logger.info(f"📊 预检查完成: {skipped_count} 个已下载, {len(artworks_to_download)} 个待下载 (耗时: {check_time:.2f}秒)")
        stats['skipped'] = skipped_count
        
        return artworks_to_download

    def _submit_download_tasks(self, artworks: List[Artwork]) -> List[tuple]:
        """提交下载任务"""
        futures = []

        self.logger.info(f"📤 开始提交 {len(artworks)} 个下载任务...")

        for i, artwork in enumerate(artworks):
            if self.state_manager.should_stop():
                self.logger.info(f"🛑 提交过程中收到停止信号，已提交 {i} 个任务")
                break

            try:
                future = self.executor.submit(self.download_artwork, artwork)
                futures.append((future, artwork))
                self._active_futures.append(future)

                # 每提交5个任务记录进度
                if (i + 1) % 5 == 0:
                    if self.state_manager.should_stop():
                        break
                    self.logger.info(f"📤 已提交 {i + 1}/{len(artworks)} 个下载任务")
                elif i == len(artworks) - 1:
                    self.logger.info(f"📤 已提交 {i + 1}/{len(artworks)} 个下载任务")

            except Exception as e:
                self.logger.error(f"❌ 提交作品 {artwork.id} 失败: {e}")

        return futures

    def _process_download_results(self, futures: List[tuple], stats: Dict[str, int]) -> None:
        """处理下载结果"""
        self.logger.info(f"⏳ 开始处理 {len(futures)} 个下载任务的结果...")

        completed_count = 0

        for future, artwork in as_completed(futures, timeout=None):
            try:
                # 检查暂停状态
                while self.state_manager.is_paused():
                    time.sleep(1)

                # 检查停止信号
                if self.state_manager.should_stop():
                    self.logger.info("🛑 收到停止信号，停止处理结果")
                    break

                # 获取结果
                success = future.result(timeout=30)  # 30秒超时

                # 更新统计
                if success:
                    stats['completed'] += 1
                    self.state_manager.add_completed_artwork(artwork.id)
                else:
                    stats['failed'] += 1
                    self.state_manager.add_failed_artwork(artwork.id)

                completed_count += 1

                # 从活跃列表中移除
                if future in self._active_futures:
                    self._active_futures.remove(future)

                # 定期输出进度
                if completed_count % 10 == 0 or completed_count == len(futures):
                    progress = completed_count / len(futures) * 100
                    self.logger.info(f"📊 处理进度: {completed_count}/{len(futures)} ({progress:.1f}%)")

            except concurrent.futures.TimeoutError:
                self.logger.warning(f"⏰ 作品 {artwork.id} 下载超时")
                stats['failed'] += 1
                self.state_manager.add_failed_artwork(artwork.id)

            except Exception as e:
                self.logger.error(f"❌ 处理作品 {artwork.id} 结果失败: {e}")
                stats['failed'] += 1
                self.state_manager.add_failed_artwork(artwork.id)

    def _log_final_stats(self, stats: Dict[str, int]) -> None:
        """输出最终统计信息"""
        total = stats['total']
        completed = stats['completed']
        failed = stats['failed']
        skipped = stats['skipped']

        success_rate = (completed / total * 100) if total > 0 else 0

        self.logger.info("=" * 50)
        self.logger.info("📊 下载完成统计:")
        self.logger.info(f"   总计: {total} 个作品")
        self.logger.info(f"   成功: {completed} 个 ({success_rate:.1f}%)")
        self.logger.info(f"   失败: {failed} 个")
        self.logger.info(f"   跳过: {skipped} 个")
        self.logger.info("=" * 50)

    def check_artwork_downloaded(self, artwork: Artwork) -> tuple[bool, str]:
        """
        检查作品是否已下载

        Args:
            artwork: 作品对象

        Returns:
            Tuple[bool, str]: (是否已下载, 保存路径)
        """
        return self.validator.check_artwork_downloaded(artwork)

    def generate_save_path(self, artwork: Artwork) -> str:
        """
        生成作品保存路径

        Args:
            artwork: 作品对象

        Returns:
            str: 保存路径
        """
        return self.processor.generate_save_path(artwork)
