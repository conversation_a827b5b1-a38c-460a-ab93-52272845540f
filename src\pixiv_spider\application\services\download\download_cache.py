﻿"""
下载缓存管理器

专门负责管理已下载作品的缓存，提供快速的重复检查功能
"""

import os
import re
import logging
import time
from typing import Dict, Set, Optional
from pathlib import Path

from ....models.config import DownloadConfig


class DownloadCache:
    """下载缓存管理器 - 专注缓存管理功能"""

    def __init__(self, download_config: DownloadConfig):
        """
        初始化下载缓存管理器

        Args:
            download_config: 下载配置
        """
        self.download_config = download_config
        self.logger = logging.getLogger(__name__)

        # 缓存数据
        self._cache: Dict[str, str] = {}  # artwork_id -> folder_path
        self._initialized = False

        # 性能优化
        self._processed_folders: Set[str] = set()

    def is_initialized(self) -> bool:
        """检查缓存是否已初始化"""
        return self._initialized

    def ensure_initialized(self) -> None:
        """确保缓存已初始化"""
        if not self._initialized:
            self.logger.info("🔍 初始化下载缓存...")
            start_time = time.time()
            self._scan_downloaded_artworks()
            self._initialized = True
            init_time = time.time() - start_time
            self.logger.info(f"✅ 缓存初始化完成 (耗时: {init_time:.2f}秒, 发现 {len(self._cache)} 个已下载作品)")

    def is_downloaded(self, artwork_id: str) -> bool:
        """
        检查作品是否已下载

        Args:
            artwork_id: 作品ID

        Returns:
            bool: 是否已下载
        """
        self.ensure_initialized()
        return artwork_id in self._cache

    def get_downloaded_path(self, artwork_id: str) -> Optional[str]:
        """
        获取已下载作品的路径

        Args:
            artwork_id: 作品ID

        Returns:
            Optional[str]: 作品路径，如果未下载则返回None
        """
        self.ensure_initialized()
        return self._cache.get(artwork_id)

    def add_to_cache(self, artwork_id: str, folder_path: str) -> None:
        """
        添加作品到缓存

        Args:
            artwork_id: 作品ID
            folder_path: 文件夹路径
        """
        self._cache[artwork_id] = folder_path
        self.logger.debug(f"📝 添加到缓存: {artwork_id} -> {folder_path}")

    def remove_from_cache(self, artwork_id: str) -> None:
        """
        从缓存中移除作品

        Args:
            artwork_id: 作品ID
        """
        if artwork_id in self._cache:
            del self._cache[artwork_id]
            self.logger.debug(f"🗑️ 从缓存移除: {artwork_id}")

    def refresh(self) -> None:
        """刷新缓存"""
        self.logger.info("🔄 刷新下载缓存...")
        self._cache.clear()
        self._processed_folders.clear()
        self._initialized = False
        self.ensure_initialized()

    def get_cache_stats(self) -> Dict[str, int]:
        """
        获取缓存统计信息

        Returns:
            Dict[str, int]: 缓存统计
        """
        return {
            'total_cached': len(self._cache),
            'initialized': self._initialized
        }

    def _scan_downloaded_artworks(self) -> None:
        """扫描已下载的作品"""
        if not os.path.exists(self.download_config.save_path):
            self.logger.warning(f"⚠️ 下载目录不存在: {self.download_config.save_path}")
            return

        downloaded_count = 0

        try:
            # 使用os.walk进行单次遍历，比多次glob更高效
            for root, dirs, files in os.walk(self.download_config.save_path):
                # 限制遍历深度为2层，避免过深遍历
                level = root.replace(self.download_config.save_path, '').count(os.sep)
                if level >= 2:
                    dirs[:] = []  # 不再深入子目录
                    continue

                for dir_name in dirs:
                    folder_path = os.path.join(root, dir_name)

                    if folder_path in self._processed_folders:
                        continue
                    self._processed_folders.add(folder_path)

                    # 快速提取作品ID
                    artwork_id = self._extract_artwork_id(dir_name)
                    if artwork_id:
                        # 快速检查是否有图片文件
                        if self._has_images(folder_path, fast_mode=True):
                            self._cache[artwork_id] = folder_path
                            downloaded_count += 1

        except Exception as e:
            self.logger.error(f"❌ 扫描已下载作品时出错: {e}")

        self.logger.debug(f"📊 扫描完成，发现 {downloaded_count} 个已下载作品")

    def _extract_artwork_id(self, folder_name: str) -> Optional[str]:
        """
        从文件夹名称提取作品ID

        Args:
            folder_name: 文件夹名称

        Returns:
            Optional[str]: 作品ID，如果提取失败则返回None
        """
        # 匹配各种可能的文件夹命名格式
        patterns = [
            r'^(\d+)_',           # 123456_title
            r'^(\d+)-',           # 123456-title
            r'^(\d+)\s',          # 123456 title
            r'^\[(\d+)\]',        # [123456]title
            r'^(\d+)$',           # 123456
            r'_(\d+)_',           # prefix_123456_suffix
        ]

        for pattern in patterns:
            match = re.search(pattern, folder_name)
            if match:
                return match.group(1)

        return None

    def _has_images(self, folder_path: str, fast_mode: bool = True) -> bool:
        """
        检查文件夹是否有图片文件

        Args:
            folder_path: 文件夹路径
            fast_mode: 快速模式，只检查前几个文件

        Returns:
            bool: 是否包含图片文件
        """
        try:
            image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'}
            count = 0

            for file in os.listdir(folder_path):
                if fast_mode and count >= 3:  # 快速模式只检查前3个文件
                    break

                if any(file.lower().endswith(ext) for ext in image_extensions):
                    return True

                if fast_mode:
                    count += 1

            return False
        except Exception:
            return False
