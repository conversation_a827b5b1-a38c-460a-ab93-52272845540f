<template>
  <el-card class="stats-card modern-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="header-left">
          <el-icon class="header-icon"><DataAnalysis /></el-icon>
          <span class="header-title">下载统计</span>
        </div>
        <div class="header-actions">
          <el-button size="small" @click="resetStats" :disabled="isDownloading" class="modern-button">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </div>
      </div>
    </template>
    
    <div class="stats-content">
      <!-- 统计卡片 -->
      <div class="stats-grid">
        <div class="stat-item success">
          <div class="stat-icon">
            <el-icon><SuccessFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.downloaded }}</div>
            <div class="stat-label">已下载</div>
          </div>
        </div>
        
        <div class="stat-item danger">
          <div class="stat-icon">
            <el-icon><CircleCloseFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.failed }}</div>
            <div class="stat-label">失败</div>
          </div>
        </div>
        
        <div class="stat-item warning">
          <div class="stat-icon">
            <el-icon><WarningFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.skipped }}</div>
            <div class="stat-label">跳过</div>
          </div>
        </div>
        
        <div class="stat-item info">
          <div class="stat-icon">
            <el-icon><DataLine /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ totalProcessed }}</div>
            <div class="stat-label">总计</div>
          </div>
        </div>
      </div>
      
      <!-- 成功率显示 -->
      <div class="success-rate">
        <div class="rate-header">
          <span class="rate-label">成功率</span>
          <span class="rate-value">{{ successRate }}%</span>
        </div>
        <el-progress 
          :percentage="successRate" 
          :color="successRateColor"
          :stroke-width="8"
          :show-text="false"
        />
      </div>
      
      <!-- 详细统计 -->
      <div class="detailed-stats">
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="平均速度">
            {{ averageSpeed }} 张/分钟
          </el-descriptions-item>
          <el-descriptions-item label="最大并发">
            {{ maxConcurrent }}
          </el-descriptions-item>
          <el-descriptions-item label="重试次数">
            {{ totalRetries }}
          </el-descriptions-item>
          <el-descriptions-item label="数据传输">
            {{ formatBytes(totalBytes) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </el-card>
</template>

<script>
import { mapState } from 'vuex'
import { 
  DataAnalysis, 
  RefreshLeft, 
  SuccessFilled, 
  CircleCloseFilled, 
  WarningFilled, 
  DataLine 
} from '@element-plus/icons-vue'

export default {
  name: 'StatsDisplay',
  components: {
    DataAnalysis,
    RefreshLeft,
    SuccessFilled,
    CircleCloseFilled,
    WarningFilled,
    DataLine
  },
  data() {
    return {
      totalRetries: 0,
      totalBytes: 0,
      averageSpeed: 0,
      maxConcurrent: 3
    }
  },
  computed: {
    ...mapState(['isDownloading', 'downloadStats', 'downloadSettings']),
    
    stats() {
      const result = {
        downloaded: this.downloadStats.success || this.downloadStats.completed || 0,
        failed: this.downloadStats.failed || 0,
        skipped: this.downloadStats.skipped || 0,
        total: this.downloadStats.total || 0
      }
      console.log('📈 StatsDisplay计算属性:', result, '原始数据:', this.downloadStats)
      return result
    },
    
    totalProcessed() {
      return this.stats.downloaded + this.stats.failed + this.stats.skipped
    },
    
    successRate() {
      if (this.totalProcessed === 0) return 0
      return Math.round((this.stats.downloaded / this.totalProcessed) * 100)
    },
    
    successRateColor() {
      if (this.successRate >= 90) return 'var(--success-color)'
      if (this.successRate >= 70) return 'var(--warning-color)'
      return 'var(--error-color)'
    }
  },
  
  methods: {
    resetStats() {
      this.$store.commit('updateDownloadStats', {
        downloaded: 0,
        failed: 0,
        skipped: 0
      })
      
      this.totalRetries = 0
      this.totalBytes = 0
      this.averageSpeed = 0
      
      this.$message.success('统计数据已重置')
    },
    
    formatBytes(bytes) {
      if (bytes === 0) return '0 B'
      
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    
    updateAverageSpeed() {
      // 模拟计算平均速度
      if (this.isDownloading && this.stats.downloaded > 0) {
        // 这里应该基于实际的时间计算
        this.averageSpeed = Math.random() * 10 + 5
      }
    },
    
    simulateDataTransfer() {
      // 模拟数据传输统计
      if (this.isDownloading) {
        this.totalBytes += Math.random() * 1024 * 1024 // 随机增加1MB以内
      }
    }
  },
  
  mounted() {
    // 定期更新统计数据
    setInterval(() => {
      this.updateAverageSpeed()
      this.simulateDataTransfer()
    }, 2000)
    
    // 从设置中获取最大并发数
    this.maxConcurrent = this.downloadSettings.maxConcurrent || 3
  },
  
  watch: {
    'downloadSettings.maxConcurrent'(newVal) {
      this.maxConcurrent = newVal
    }
  }
}
</script>

<style scoped>
.stats-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
}

.card-header > div:first-child {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  border-radius: 8px;
  transition: transform 0.2s;
}

.stat-item:hover {
  transform: translateY(-2px);
}

.stat-item.success {
  background: var(--bg-secondary);
  border: 1px solid var(--success-color);
}

.stat-item.danger {
  background: var(--bg-secondary);
  border: 1px solid var(--error-color);
}

.stat-item.warning {
  background: var(--bg-secondary);
  border: 1px solid var(--warning-color);
}

.stat-item.info {
  background: var(--bg-secondary);
  border: 1px solid var(--accent-color);
}

.stat-icon {
  font-size: 24px;
}

.stat-item.success .stat-icon {
  color: var(--success-color);
}

.stat-item.danger .stat-icon {
  color: var(--error-color);
}

.stat-item.warning .stat-icon {
  color: var(--warning-color);
}

.stat-item.info .stat-icon {
  color: var(--accent-color);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 4px;
}

.success-rate {
  padding: 15px;
  background: var(--bg-secondary);
  border-radius: 8px;
}

.rate-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.rate-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.rate-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.detailed-stats {
  margin-top: 10px;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
