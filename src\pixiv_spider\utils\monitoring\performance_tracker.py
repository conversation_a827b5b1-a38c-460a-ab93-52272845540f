"""
性能跟踪器

跟踪和分析应用程序性能指标
"""

import time
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from collections import defaultdict


@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def finish(self) -> float:
        """完成计时"""
        if self.end_time is None:
            self.end_time = time.time()
            self.duration = self.end_time - self.start_time
        return self.duration


class PerformanceTracker:
    """性能跟踪器"""
    
    def __init__(self):
        """初始化性能跟踪器"""
        self.logger = logging.getLogger(__name__)
        
        # 性能指标存储
        self.metrics: List[PerformanceMetric] = []
        self.active_metrics: Dict[str, PerformanceMetric] = {}
        
        # 统计数据
        self.stats: Dict[str, List[float]] = defaultdict(list)
        
        # 配置
        self.max_metrics = 1000  # 最大保存的指标数量
        
    def start_timing(self, name: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        开始计时
        
        Args:
            name: 指标名称
            metadata: 元数据
            
        Returns:
            str: 计时ID
        """
        metric_id = f"{name}_{time.time()}"
        metric = PerformanceMetric(
            name=name,
            start_time=time.time(),
            metadata=metadata or {}
        )
        
        self.active_metrics[metric_id] = metric
        self.logger.debug(f"⏱️ 开始计时: {name}")
        
        return metric_id
    
    def stop_timing(self, metric_id: str) -> Optional[float]:
        """
        停止计时
        
        Args:
            metric_id: 计时ID
            
        Returns:
            Optional[float]: 耗时（秒）
        """
        if metric_id not in self.active_metrics:
            self.logger.warning(f"未找到计时器: {metric_id}")
            return None
        
        metric = self.active_metrics.pop(metric_id)
        duration = metric.finish()
        
        # 保存指标
        self._add_metric(metric)
        
        self.logger.debug(f"⏱️ 计时完成: {metric.name} - {duration:.3f}秒")
        return duration
    
    def time_operation(self, name: str, metadata: Optional[Dict[str, Any]] = None):
        """
        上下文管理器，用于计时操作
        
        Args:
            name: 操作名称
            metadata: 元数据
        """
        return TimingContext(self, name, metadata)
    
    def record_value(self, name: str, value: float, metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        记录数值指标
        
        Args:
            name: 指标名称
            value: 数值
            metadata: 元数据
        """
        metric = PerformanceMetric(
            name=name,
            start_time=time.time(),
            end_time=time.time(),
            duration=value,
            metadata=metadata or {}
        )
        
        self._add_metric(metric)
        self.logger.debug(f"📊 记录指标: {name} = {value}")
    
    def _add_metric(self, metric: PerformanceMetric) -> None:
        """添加指标"""
        self.metrics.append(metric)
        self.stats[metric.name].append(metric.duration)
        
        # 限制指标数量
        if len(self.metrics) > self.max_metrics:
            # 移除最旧的指标
            old_metric = self.metrics.pop(0)
            if old_metric.name in self.stats and self.stats[old_metric.name]:
                self.stats[old_metric.name].pop(0)
    
    def get_stats(self, name: str) -> Dict[str, Any]:
        """
        获取指标统计信息
        
        Args:
            name: 指标名称
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        if name not in self.stats or not self.stats[name]:
            return {}
        
        values = self.stats[name]
        
        return {
            'count': len(values),
            'min': min(values),
            'max': max(values),
            'avg': sum(values) / len(values),
            'total': sum(values),
            'recent': values[-10:] if len(values) > 10 else values
        }
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有指标的统计信息"""
        return {name: self.get_stats(name) for name in self.stats.keys()}
    
    def get_recent_metrics(self, count: int = 10) -> List[PerformanceMetric]:
        """
        获取最近的指标
        
        Args:
            count: 数量
            
        Returns:
            List[PerformanceMetric]: 最近的指标列表
        """
        return self.metrics[-count:] if len(self.metrics) > count else self.metrics
    
    def get_metrics_by_name(self, name: str) -> List[PerformanceMetric]:
        """
        根据名称获取指标
        
        Args:
            name: 指标名称
            
        Returns:
            List[PerformanceMetric]: 指标列表
        """
        return [m for m in self.metrics if m.name == name]
    
    def clear_metrics(self) -> None:
        """清理所有指标"""
        cleared_count = len(self.metrics)
        self.metrics.clear()
        self.stats.clear()
        self.active_metrics.clear()
        
        self.logger.info(f"🧹 已清理 {cleared_count} 个性能指标")
    
    def get_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        total_metrics = len(self.metrics)
        active_timers = len(self.active_metrics)
        
        # 计算总体统计
        if self.metrics:
            all_durations = [m.duration for m in self.metrics if m.duration is not None]
            if all_durations:
                avg_duration = sum(all_durations) / len(all_durations)
                max_duration = max(all_durations)
                min_duration = min(all_durations)
            else:
                avg_duration = max_duration = min_duration = 0
        else:
            avg_duration = max_duration = min_duration = 0
        
        return {
            'total_metrics': total_metrics,
            'active_timers': active_timers,
            'unique_metric_names': len(self.stats),
            'avg_duration': avg_duration,
            'max_duration': max_duration,
            'min_duration': min_duration,
            'metric_names': list(self.stats.keys())
        }
    
    def export_metrics(self, format: str = 'dict') -> Any:
        """
        导出指标数据
        
        Args:
            format: 导出格式 ('dict', 'json')
            
        Returns:
            Any: 导出的数据
        """
        data = {
            'metrics': [
                {
                    'name': m.name,
                    'start_time': m.start_time,
                    'end_time': m.end_time,
                    'duration': m.duration,
                    'metadata': m.metadata
                }
                for m in self.metrics
            ],
            'stats': dict(self.stats),
            'summary': self.get_summary()
        }
        
        if format == 'json':
            import json
            return json.dumps(data, indent=2)
        
        return data


class TimingContext:
    """计时上下文管理器"""
    
    def __init__(self, tracker: PerformanceTracker, name: str, metadata: Optional[Dict[str, Any]] = None):
        self.tracker = tracker
        self.name = name
        self.metadata = metadata
        self.metric_id: Optional[str] = None
    
    def __enter__(self):
        self.metric_id = self.tracker.start_timing(self.name, self.metadata)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.metric_id:
            self.tracker.stop_timing(self.metric_id)
