"""
日志格式化器

提供统一的日志格式化和标准化日志记录
"""

import logging
import time
from typing import Any, Dict, Optional


class LogFormatter:
    """日志格式化工具"""
    
    @staticmethod
    def format_progress(current: int, total: int, prefix: str = "", suffix: str = "") -> str:
        """
        格式化进度信息
        
        Args:
            current: 当前进度
            total: 总数
            prefix: 前缀
            suffix: 后缀
            
        Returns:
            str: 格式化的进度字符串
        """
        if total == 0:
            percentage = 0
        else:
            percentage = (current / total) * 100
        
        progress_bar = LogFormatter._create_progress_bar(percentage)
        
        return f"{prefix} {progress_bar} {current}/{total} ({percentage:.1f}%) {suffix}".strip()
    
    @staticmethod
    def _create_progress_bar(percentage: float, width: int = 20) -> str:
        """创建进度条"""
        filled = int(width * percentage / 100)
        bar = "█" * filled + "░" * (width - filled)
        return f"[{bar}]"
    
    @staticmethod
    def format_size(size_bytes: int) -> str:
        """
        格式化文件大小
        
        Args:
            size_bytes: 字节数
            
        Returns:
            str: 格式化的大小字符串
        """
        if size_bytes == 0:
            return "0B"
        
        units = ["B", "KB", "MB", "GB", "TB"]
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        if unit_index == 0:
            return f"{int(size)}{units[unit_index]}"
        else:
            return f"{size:.1f}{units[unit_index]}"
    
    @staticmethod
    def format_duration(seconds: float) -> str:
        """
        格式化时间长度
        
        Args:
            seconds: 秒数
            
        Returns:
            str: 格式化的时间字符串
        """
        if seconds < 60:
            return f"{seconds:.1f}秒"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            remaining_seconds = seconds % 60
            return f"{minutes}分{remaining_seconds:.1f}秒"
        else:
            hours = int(seconds // 3600)
            remaining_minutes = int((seconds % 3600) // 60)
            remaining_seconds = seconds % 60
            return f"{hours}小时{remaining_minutes}分{remaining_seconds:.1f}秒"
    
    @staticmethod
    def format_speed(items_per_second: float, unit: str = "项") -> str:
        """
        格式化速度
        
        Args:
            items_per_second: 每秒处理的项目数
            unit: 单位
            
        Returns:
            str: 格式化的速度字符串
        """
        if items_per_second < 1:
            return f"{items_per_second:.2f} {unit}/秒"
        else:
            return f"{items_per_second:.1f} {unit}/秒"
    
    @staticmethod
    def format_error(error: Exception, context: Optional[str] = None) -> str:
        """
        格式化错误信息
        
        Args:
            error: 异常对象
            context: 上下文信息
            
        Returns:
            str: 格式化的错误字符串
        """
        error_type = type(error).__name__
        error_msg = str(error)
        
        if context:
            return f"❌ {context}: {error_type} - {error_msg}"
        else:
            return f"❌ {error_type}: {error_msg}"
    
    @staticmethod
    def format_success(message: str, details: Optional[Dict[str, Any]] = None) -> str:
        """
        格式化成功信息
        
        Args:
            message: 成功消息
            details: 详细信息
            
        Returns:
            str: 格式化的成功字符串
        """
        result = f"✅ {message}"
        
        if details:
            detail_parts = []
            for key, value in details.items():
                detail_parts.append(f"{key}: {value}")
            
            if detail_parts:
                result += f" ({', '.join(detail_parts)})"
        
        return result
    
    @staticmethod
    def format_warning(message: str, suggestion: Optional[str] = None) -> str:
        """
        格式化警告信息
        
        Args:
            message: 警告消息
            suggestion: 建议
            
        Returns:
            str: 格式化的警告字符串
        """
        result = f"⚠️ {message}"
        
        if suggestion:
            result += f" (建议: {suggestion})"
        
        return result


class StandardLogger:
    """标准化日志记录器"""
    
    def __init__(self, name: str, level: int = logging.INFO):
        """
        初始化标准日志记录器
        
        Args:
            name: 日志记录器名称
            level: 日志级别
        """
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def log_progress(self, current: int, total: int, operation: str = "处理") -> None:
        """记录进度日志"""
        message = LogFormatter.format_progress(current, total, f"📊 {operation}进度:")
        self.logger.info(message)
    
    def log_success(self, message: str, details: Optional[Dict[str, Any]] = None) -> None:
        """记录成功日志"""
        formatted_message = LogFormatter.format_success(message, details)
        self.logger.info(formatted_message)
    
    def log_error(self, error: Exception, context: Optional[str] = None) -> None:
        """记录错误日志"""
        formatted_message = LogFormatter.format_error(error, context)
        self.logger.error(formatted_message)
    
    def log_warning(self, message: str, suggestion: Optional[str] = None) -> None:
        """记录警告日志"""
        formatted_message = LogFormatter.format_warning(message, suggestion)
        self.logger.warning(formatted_message)
    
    def log_operation_start(self, operation: str, details: Optional[Dict[str, Any]] = None) -> float:
        """
        记录操作开始日志
        
        Args:
            operation: 操作名称
            details: 详细信息
            
        Returns:
            float: 开始时间戳
        """
        start_time = time.time()
        message = f"🚀 开始{operation}"
        
        if details:
            detail_parts = []
            for key, value in details.items():
                detail_parts.append(f"{key}: {value}")
            message += f" ({', '.join(detail_parts)})"
        
        self.logger.info(message)
        return start_time
    
    def log_operation_end(self, operation: str, start_time: float, 
                         success: bool = True, details: Optional[Dict[str, Any]] = None) -> None:
        """
        记录操作结束日志
        
        Args:
            operation: 操作名称
            start_time: 开始时间戳
            success: 是否成功
            details: 详细信息
        """
        duration = time.time() - start_time
        duration_str = LogFormatter.format_duration(duration)
        
        if success:
            emoji = "✅"
            status = "完成"
        else:
            emoji = "❌"
            status = "失败"
        
        message = f"{emoji} {operation}{status}，耗时: {duration_str}"
        
        if details:
            detail_parts = []
            for key, value in details.items():
                detail_parts.append(f"{key}: {value}")
            message += f" ({', '.join(detail_parts)})"
        
        if success:
            self.logger.info(message)
        else:
            self.logger.error(message)


def get_standard_logger(name: str, level: int = logging.INFO) -> StandardLogger:
    """
    获取标准日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别
        
    Returns:
        StandardLogger: 标准日志记录器实例
    """
    return StandardLogger(name, level)
