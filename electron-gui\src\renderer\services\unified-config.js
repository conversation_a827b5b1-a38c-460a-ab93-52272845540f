/**
 * 统一配置管理服务
 * 解决前后端配置不统一和重复转换的问题
 */

import validationService from './validation.js'

class UnifiedConfigService {
  constructor() {
    this.defaultConfig = {
      // 基础配置
      save_path: '',
      skip_existing: true,
      create_info_file: true,
      download_limit: 0,

      // 模式配置
      download_mode: 'date', // date, search, ranking, user

      // 文件分类配置
      classify_mode: 'by_date', // by_date, by_author, by_type, flat

      // 关注模式配置
      start_page: 1,
      end_page: 5,
      days: 7,
      date_mode: 'by_page_range', // by_page_range, by_date_range

      // 搜索模式配置
      search_keyword: '',
      search_save_path: '',
      min_bookmarks: 0,
      search_mode: 'all', // all, safe, r18
      search_config: {
        category: '综合',
        bookmark_count: -1,
        content_mode: '全部'
      },

      // 排行榜模式配置
      ranking_category: 'overall',

      // 画师模式配置
      user_id: 0,
      user_save_path: ''
    }
  }

  /**
   * 从前端设置创建统一配置
   */
  createUnifiedConfig(mode, settings) {
    const config = { ...this.defaultConfig }

    // 设置基础路径
    const downloadPath = this._getDownloadPath(mode, settings)
    config.save_path = downloadPath

    // 设置文件分类模式
    if (settings.classifyMode) {
      config.classify_mode = settings.classifyMode
    }

    switch (mode) {
      case 'following':
        this._configureFollowing(config, settings)
        break
      case 'search':
        this._configureSearch(config, settings, downloadPath)
        break
      case 'ranking':
        this._configureRanking(config, settings)
        break
      case 'artist':
        this._configureArtist(config, settings, downloadPath)
        break
      default:
        throw new Error(`未知的下载模式: ${mode}`)
    }

    return config
  }

  /**
   * 配置关注模式
   */
  _configureFollowing(config, settings) {
    config.download_mode = 'date'
    config.start_page = settings?.pageStart || 1
    config.end_page = settings?.pageEnd || 5
    config.days = settings?.days || 7
    config.date_mode = settings?.downloadType === 'days' ? 'by_date_range' : 'by_page_range'
  }

  /**
   * 配置搜索模式
   */
  _configureSearch(config, settings, downloadPath) {
    config.download_mode = 'search'
    config.search_keyword = settings?.keyword || ''
    config.search_save_path = downloadPath
    config.start_page = settings?.pageStart || 1
    config.end_page = settings?.pageEnd || 5
    config.search_mode = settings?.searchMode || 'all'
    
    // 收藏过滤配置
    if (settings?.enableBookmarkFilter && settings?.bookmarkCount) {
      config.min_bookmarks = settings.bookmarkCount
      config.search_config = {
        category: this._mapSearchTypeToCategory(settings.searchType),
        bookmark_count: settings.bookmarkCount,
        content_mode: this._mapSearchModeToContentMode(settings.searchMode)
      }
    } else {
      config.min_bookmarks = 0
      config.search_config = {
        category: this._mapSearchTypeToCategory(settings?.searchType),
        bookmark_count: -1,
        content_mode: this._mapSearchModeToContentMode(settings?.searchMode)
      }
    }
  }

  /**
   * 配置排行榜模式
   */
  _configureRanking(config, settings) {
    config.download_mode = 'ranking'
    config.ranking_category = settings?.rankingType || 'overall'
    config.start_page = 1
    config.end_page = 1
  }

  /**
   * 配置画师模式
   */
  _configureArtist(config, settings, downloadPath) {
    config.download_mode = 'user'
    config.user_id = settings?.artistId ? Number(settings.artistId) : 0
    config.user_save_path = downloadPath
    config.start_page = settings?.pageStart || 1
    config.end_page = settings?.pageEnd || 5
  }

  /**
   * 获取下载路径
   */
  _getDownloadPath(mode, settings) {
    switch (mode) {
      case 'following':
        return settings?.downloadPath || ''
      case 'search':
        return settings?.downloadPath || ''
      case 'ranking':
        return settings?.downloadPath || ''
      case 'artist':
        return settings?.downloadPath || ''
      default:
        return ''
    }
  }

  /**
   * 映射搜索类型到分类
   */
  _mapSearchTypeToCategory(searchType) {
    const mapping = {
      'artworks': '综合',
      'illustrations': '插画',
      'manga': '漫画'
    }
    return mapping[searchType] || '综合'
  }

  /**
   * 映射搜索模式到内容模式
   */
  _mapSearchModeToContentMode(searchMode) {
    const mapping = {
      'all': '全部',
      'safe': '全年龄',
      'r18': 'R18'
    }
    return mapping[searchMode] || '全部'
  }

  /**
   * 验证配置 - 使用统一验证服务
   */
  validateConfig(config) {
    return validationService.validateDownloadConfig(config)
  }

  /**
   * 生成预览URL
   */
  generatePreviewUrl(mode, settings) {
    switch (mode) {
      case 'search':
        const keyword = settings?.keyword || ''
        const searchType = settings?.searchType || 'artworks'

        if (settings?.enableBookmarkFilter && settings?.bookmarkCount) {
          const searchTerm = `${keyword}${settings.bookmarkCount}users入り`
          return `https://www.pixiv.net/tags/${encodeURIComponent(searchTerm)}/${searchType}`
        } else {
          return `https://www.pixiv.net/tags/${encodeURIComponent(keyword)}/${searchType}`
        }

      case 'following':
        return 'https://www.pixiv.net/bookmark_new_illust.php'

      case 'ranking':
        const rankingType = settings?.rankingType || 'overall'
        return `https://www.pixiv.net/ranking.php?mode=${rankingType}`

      case 'artist':
        const artistId = settings?.artistId || ''
        if (!artistId) return ''

        // 生成画师作品URL，支持页码参数
        const page = settings?.pageStart || 1
        const baseUrl = `https://www.pixiv.net/users/${artistId}/artworks`
        return page > 1 ? `${baseUrl}?p=${page}` : baseUrl

      default:
        return ''
    }
  }

  /**
   * 生成画师作品URL（支持指定页码）
   */
  generateArtistUrl(artistId, page = 1) {
    if (!artistId) {
      throw new Error('画师作品模式下必须提供画师ID')
    }

    const baseUrl = `https://www.pixiv.net/users/${artistId}/artworks`
    return page > 1 ? `${baseUrl}?p=${page}` : baseUrl
  }
}

// 创建单例实例
const unifiedConfigService = new UnifiedConfigService()

export default unifiedConfigService
