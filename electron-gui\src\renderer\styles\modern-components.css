/* Pixiv Spider v6.1 现代化组件样式 */

/* 现代化卡片样式 */
.modern-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-md) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  overflow: hidden !important;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%) !important;
  border: 1px solid var(--border-color) !important;
}

.modern-card:hover {
  box-shadow: var(--shadow-lg) !important;
  transform: translateY(-2px) !important;
}

.modern-card .el-card__header {
  background: var(--bg-primary) !important;
  border-bottom: 1px solid var(--border-color) !important;
  padding: 20px !important;
}

.modern-card .el-card__body {
  background: var(--bg-primary) !important;
  padding: 20px !important;
}

/* 现代化按钮组 */
.modern-button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.modern-button-group .el-button {
  border-radius: var(--radius-lg) !important;
  font-weight: 500 !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: var(--shadow-xs) !important;
  min-height: 40px !important;
  padding: 8px 16px !important;
}

.modern-button-group .el-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-md) !important;
}

/* 现代化表单样式 */
.modern-form .el-form-item {
  margin-bottom: 20px;
}

.modern-form .el-form-item__label {
  color: var(--text-secondary) !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  margin-bottom: 8px !important;
}

.modern-form .el-input__wrapper,
.modern-form .el-textarea__inner,
.modern-form .el-select .el-input__wrapper {
  border-radius: var(--radius-lg) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: var(--shadow-xs) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.modern-form .el-input__wrapper:hover,
.modern-form .el-textarea__inner:hover,
.modern-form .el-select .el-input__wrapper:hover {
  border-color: var(--primary-color) !important;
  box-shadow: var(--shadow-sm) !important;
}

.modern-form .el-input__wrapper.is-focus,
.modern-form .el-textarea__inner:focus,
.modern-form .el-select .el-input__wrapper.is-focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px var(--primary-light) !important;
}

/* 现代化进度条 */
.modern-progress .el-progress-bar__outer {
  background-color: var(--bg-tertiary) !important;
  border-radius: var(--radius-full) !important;
  overflow: hidden !important;
  height: 12px !important;
}

.modern-progress .el-progress-bar__inner {
  background: var(--gradient-primary) !important;
  border-radius: var(--radius-full) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 现代化标签 */
.modern-tag {
  border-radius: var(--radius-md) !important;
  font-weight: 500 !important;
  padding: 6px 12px !important;
  border: none !important;
  box-shadow: var(--shadow-xs) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.modern-tag:hover {
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-sm) !important;
}

/* 现代化状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: var(--radius-full);
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-indicator.success {
  background: var(--success-light);
  color: var(--success-color);
}

.status-indicator.warning {
  background: var(--warning-light);
  color: var(--warning-color);
}

.status-indicator.danger {
  background: var(--danger-light);
  color: var(--danger-color);
}

.status-indicator.info {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

/* 现代化统计卡片 */
.stats-card {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  border-radius: var(--radius-xl);
  padding: 20px;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stats-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stats-card .stats-icon {
  font-size: 24px;
  color: var(--primary-color);
  background: var(--primary-light);
  padding: 12px;
  border-radius: var(--radius-lg);
  margin-bottom: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.stats-card .stats-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stats-card .stats-label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

/* 现代化日志显示 */
.modern-log {
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-inner);
  overflow: hidden;
}

.modern-log .log-header {
  background: var(--bg-primary);
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modern-log .log-content {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
}

.modern-log .log-item {
  margin-bottom: 8px;
  padding: 8px 12px;
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  border-left: 3px solid var(--border-color);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-log .log-item:hover {
  background: var(--bg-tertiary);
  transform: translateX(4px);
}

.modern-log .log-item.error {
  border-left-color: var(--danger-color);
  background: var(--danger-light);
}

.modern-log .log-item.warning {
  border-left-color: var(--warning-color);
  background: var(--warning-light);
}

.modern-log .log-item.success {
  border-left-color: var(--success-color);
  background: var(--success-light);
}

/* 现代化加载动画 */
.modern-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 20px;
}

.modern-loading .loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid var(--bg-tertiary);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.modern-loading .loading-text {
  color: var(--text-secondary);
  font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .modern-button-group {
    gap: 8px;
  }
  
  .modern-button-group .el-button {
    min-height: 36px !important;
    padding: 6px 12px !important;
    font-size: 13px !important;
  }
  
  .stats-card {
    padding: 16px;
  }
  
  .stats-card .stats-icon {
    font-size: 20px;
    padding: 10px;
    margin-bottom: 12px;
  }
  
  .stats-card .stats-value {
    font-size: 24px;
  }
  
  .modern-log .log-content {
    padding: 16px;
    font-size: 12px;
  }
  
  .modern-log .log-header {
    padding: 12px 16px;
  }
}
