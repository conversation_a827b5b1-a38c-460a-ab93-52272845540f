"""
HTTP客户端

提供通用的HTTP请求功能，包括错误处理和响应处理
"""

import logging
import requests
from typing import Optional, Dict, Any, Callable
from requests import Response

from ...models.exceptions import NetworkError, AuthenticationError
from ...models.config import SpiderConfig
from .session_manager import SessionManager


class HttpClient:
    """HTTP客户端 - 专注通用HTTP请求功能"""
    
    def __init__(self, session_manager: SessionManager, spider_config: SpiderConfig):
        """
        初始化HTTP客户端
        
        Args:
            session_manager: 会话管理器
            spider_config: 爬虫配置
        """
        self.session_manager = session_manager
        self.spider_config = spider_config
        self.logger = logging.getLogger(__name__)
    
    def make_request(self, url: str, method: str = 'GET', 
                    stop_signal_callback: Optional[Callable] = None,
                    **kwargs) -> Optional[Response]:
        """
        通用请求方法
        
        Args:
            url: 请求URL
            method: 请求方法
            stop_signal_callback: 停止信号回调函数
            **kwargs: 其他请求参数
            
        Returns:
            Optional[Response]: 响应对象，失败时返回None
        """
        # 检查停止信号
        if stop_signal_callback and stop_signal_callback():
            self.logger.debug(f"🛑 收到停止信号，取消请求: {url}")
            return None
        
        session = self.session_manager.get_session()
        try:
            # 设置默认超时
            kwargs.setdefault('timeout', self.spider_config.request_timeout)
            
            # 执行请求
            self.logger.debug(f"🌐 发起请求: {method.upper()} {url}")
            response = session.request(method.upper(), url, **kwargs)
            
            # 再次检查停止信号
            if stop_signal_callback and stop_signal_callback():
                self.logger.debug(f"🛑 请求完成后收到停止信号: {url}")
                return None
            
            return response
        
        except Exception as e:
            self._handle_request_exceptions(e, url)
            return None
        finally:
            self.session_manager.return_session(session)
    
    def make_json_request(self, url: str, method: str = 'GET',
                         stop_signal_callback: Optional[Callable] = None,
                         **kwargs) -> Optional[Dict[str, Any]]:
        """
        发起JSON请求并解析响应
        
        Args:
            url: 请求URL
            method: 请求方法
            stop_signal_callback: 停止信号回调函数
            **kwargs: 其他请求参数
            
        Returns:
            Optional[Dict[str, Any]]: 解析后的JSON数据
        """
        response = self.make_request(url, method, stop_signal_callback, **kwargs)
        if response:
            return self._handle_json_response(response, url)
        return None
    
    def download_file(self, url: str, save_path: str, 
                     stop_signal_callback: Optional[Callable] = None,
                     chunk_size: int = 65536) -> bool:
        """
        下载文件
        
        Args:
            url: 文件URL
            save_path: 保存路径
            stop_signal_callback: 停止信号回调函数
            chunk_size: 数据块大小
            
        Returns:
            bool: 是否下载成功
        """
        # 检查停止信号
        if stop_signal_callback and stop_signal_callback():
            self.logger.debug(f"🛑 收到停止信号，取消下载: {url}")
            return False
        
        session = self.session_manager.get_session()
        try:
            # 使用流式下载
            response = session.get(
                url,
                timeout=(10, 30),  # (连接超时, 读取超时)
                stream=True
            )
            
            if response.status_code == 200:
                return self._download_file_content(
                    response, save_path, stop_signal_callback, chunk_size, url
                )
            else:
                self.logger.debug(f"❌ 下载失败，状态码: {response.status_code}, URL: {url}")
                return False
        
        except Exception as e:
            self.logger.debug(f"❌ 下载文件失败: {url}, 错误: {e}")
            return False
        finally:
            self.session_manager.return_session(session)
    
    def _download_file_content(self, response: Response, save_path: str,
                              stop_signal_callback: Optional[Callable],
                              chunk_size: int, url: str) -> bool:
        """
        下载文件内容
        
        Args:
            response: 响应对象
            save_path: 保存路径
            stop_signal_callback: 停止信号回调函数
            chunk_size: 数据块大小
            url: 原始URL（用于日志）
            
        Returns:
            bool: 是否下载成功
        """
        import os
        
        try:
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    # 在每个chunk之间检查停止信号
                    if stop_signal_callback and stop_signal_callback():
                        self.logger.debug(f"🛑 下载过程中收到停止信号，中断下载: {url}")
                        # 删除不完整的文件
                        try:
                            f.close()
                            if os.path.exists(save_path):
                                os.remove(save_path)
                        except Exception:
                            pass
                        return False
                    
                    if chunk:
                        f.write(chunk)
            
            return True
        
        except Exception as e:
            self.logger.debug(f"❌ 写入文件失败: {save_path}, 错误: {e}")
            return False
    
    def _handle_json_response(self, response: Response, url: str) -> Optional[Dict[str, Any]]:
        """
        处理JSON响应
        
        Args:
            response: 响应对象
            url: 请求URL
            
        Returns:
            Optional[Dict[str, Any]]: 解析后的JSON数据
        """
        try:
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 401:
                error = AuthenticationError("API认证失败，请检查登录状态")
                self._handle_error(error, f"API请求认证失败: {url}")
                raise error
            elif response.status_code == 429:
                error = NetworkError("请求频率过高，请稍后再试")
                self._handle_error(error, f"API请求频率限制: {url}")
                raise error
            elif response.status_code == 403:
                self.logger.warning(f"⚠️ 访问被拒绝: {url} (可能需要更新cookies)")
                return None
            elif response.status_code == 404:
                self.logger.debug(f"📭 资源不存在: {url}")
                return None
            else:
                self.logger.warning(f"⚠️ API请求返回状态码: {response.status_code}, URL: {url}")
                return None
        
        except requests.exceptions.JSONDecodeError as e:
            self.logger.warning(f"⚠️ JSON解析失败: {url}, 错误: {e}")
            return None
    
    def _handle_request_exceptions(self, e: Exception, url: str) -> None:
        """
        统一的请求异常处理
        
        Args:
            e: 异常对象
            url: 请求URL
        """
        if isinstance(e, requests.exceptions.Timeout):
            error = NetworkError(f"请求超时: {url}")
            self._handle_error(error, f"HTTP请求超时: {url}")
            raise error
        elif isinstance(e, requests.exceptions.ConnectionError):
            error = NetworkError(f"连接错误: {url}")
            self._handle_error(error, f"HTTP连接错误: {url}")
            raise error
        elif isinstance(e, requests.exceptions.RequestException):
            error = NetworkError(f"请求异常: {e}")
            self._handle_error(error, f"HTTP请求异常: {url}")
            raise error
        else:
            error = NetworkError(f"未知错误: {e}")
            self._handle_error(error, f"HTTP请求未知错误: {url}")
            raise error
    
    def _handle_error(self, error: Exception, context: str) -> None:
        """
        处理错误（使用统一错误处理器）
        
        Args:
            error: 错误对象
            context: 错误上下文
        """
        try:
            from ...utils.unified_error_handler import unified_error_handler
            unified_error_handler.handle_error(error, context)
        except ImportError:
            # 如果统一错误处理器不可用，使用基本日志记录
            self.logger.error(f"❌ {context}: {error}")
    
    def test_connection(self, test_url: str = "https://www.pixiv.net") -> bool:
        """
        测试网络连接
        
        Args:
            test_url: 测试URL
            
        Returns:
            bool: 连接是否成功
        """
        try:
            response = self.make_request(test_url, method='HEAD')
            if response:
                success = response.status_code in [200, 301, 302, 403]
                if success:
                    self.logger.debug(f"✅ 网络连接测试成功: {response.status_code}")
                else:
                    self.logger.warning(f"⚠️ 网络连接测试失败: {response.status_code}")
                return success
            return False
        
        except Exception as e:
            self.logger.warning(f"⚠️ 网络连接测试异常: {e}")
            return False
    
    def get_client_info(self) -> Dict[str, Any]:
        """
        获取客户端信息
        
        Returns:
            Dict[str, Any]: 客户端状态信息
        """
        return {
            'session_pool_status': self.session_manager.get_pool_status(),
            'request_timeout': self.spider_config.request_timeout,
            'retry_attempts': self.spider_config.retry_attempts,
            'retry_delay': self.spider_config.retry_delay,
            'proxy': self.spider_config.proxy
        }
