import { createApp } from 'vue'
import { createRouter, createWebHashHistory } from 'vue-router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
// 确保我们的主题CSS在Element Plus之后加载，以覆盖默认样式
import './styles/theme.css'
import './styles/modern-components.css'

import App from './App.vue'
import Home from './views/Home.vue'
import store from './store/index.js'
import apiService from './services/api.js'
import ipcService from './services/ipc.js'

// 路由配置
const routes = [
  { path: '/', name: 'Home', component: Home }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 主题状态管理现在在store中处理

// 创建应用
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局提供服务
app.provide('$api', apiService)
app.provide('$ipc', ipcService)

app.use(router)
app.use(store)
app.use(ElementPlus)

// 初始化应用
store.dispatch('initializeApp').catch(error => {
  console.error('应用初始化失败:', error)
})

// 加载设置并应用主题
store.dispatch('loadSettings').then(() => {
  // 应用主题
  const applyTheme = () => {
    const theme = store.state.theme
    let themeToApply = 'light'

    if (theme === 'dark') {
      themeToApply = 'dark'
    } else if (theme === 'light') {
      themeToApply = 'light'
    } else if (theme === 'auto') {
      themeToApply = (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) ? 'dark' : 'light'
    }

    // 应用主题到所有元素
    document.documentElement.setAttribute('data-theme', themeToApply)
    document.body.setAttribute('data-theme', themeToApply)

    // 移除所有主题类名
    document.body.classList.remove('dark-theme', 'light-theme', 'dark')
    document.documentElement.classList.remove('dark-theme', 'light-theme', 'dark')

    // 添加对应的主题类名
    if (themeToApply === 'dark') {
      document.body.classList.add('dark-theme', 'dark')
      document.documentElement.classList.add('dark-theme', 'dark')
    } else {
      document.body.classList.add('light-theme')
      document.documentElement.classList.add('light-theme')
    }

    // CSS变量会自动处理主题样式，无需手动设置
  }

  applyTheme()

  // 监听系统主题变化
  if (window.matchMedia) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', () => {
      if (store.state.theme === 'auto') {
        applyTheme()
      }
    })
  }
})

app.mount('#app')
