"""
文件操作工具

提供基础的文件操作功能
"""

import os
import shutil
import hashlib
import tempfile
from pathlib import Path
from typing import Optional, Tuple
import logging


class FileOperations:
    """文件操作工具类"""
    
    @staticmethod
    def copy_file(src: str, dst: str) -> bool:
        """
        复制文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            
        Returns:
            bool: 是否成功
        """
        try:
            # 确保目标目录存在
            os.makedirs(os.path.dirname(dst), exist_ok=True)
            shutil.copy2(src, dst)
            return True
        except Exception as e:
            logging.error(f"文件复制失败: {src} -> {dst}, 错误: {e}")
            return False
    
    @staticmethod
    def move_file(src: str, dst: str) -> bool:
        """
        移动文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            
        Returns:
            bool: 是否成功
        """
        try:
            os.makedirs(os.path.dirname(dst), exist_ok=True)
            shutil.move(src, dst)
            return True
        except Exception as e:
            logging.error(f"文件移动失败: {src} -> {dst}, 错误: {e}")
            return False
    
    @staticmethod
    def delete_file(file_path: str) -> bool:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否成功
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
            return True
        except Exception as e:
            logging.error(f"删除文件失败: {file_path}, 错误: {e}")
            return False
    
    @staticmethod
    def get_file_size(file_path: str) -> int:
        """
        获取文件大小
        
        Args:
            file_path: 文件路径
            
        Returns:
            int: 文件大小（字节），失败返回-1
        """
        try:
            return os.path.getsize(file_path)
        except Exception:
            return -1
    
    @staticmethod
    def get_file_hash(file_path: str, algorithm: str = 'md5') -> Optional[str]:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            algorithm: 哈希算法 ('md5', 'sha1', 'sha256')
            
        Returns:
            Optional[str]: 哈希值，失败返回None
        """
        try:
            hash_obj = hashlib.new(algorithm)
            
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            
            return hash_obj.hexdigest()
        except Exception as e:
            logging.error(f"计算文件哈希失败: {file_path}, 错误: {e}")
            return None
    
    @staticmethod
    def compare_files(file1: str, file2: str) -> bool:
        """
        比较两个文件是否相同
        
        Args:
            file1: 文件1路径
            file2: 文件2路径
            
        Returns:
            bool: 是否相同
        """
        try:
            # 先比较文件大小
            if FileOperations.get_file_size(file1) != FileOperations.get_file_size(file2):
                return False
            
            # 比较哈希值
            hash1 = FileOperations.get_file_hash(file1)
            hash2 = FileOperations.get_file_hash(file2)
            
            return hash1 == hash2 and hash1 is not None
        except Exception:
            return False
    
    @staticmethod
    def create_temp_file(suffix: str = "", prefix: str = "pixiv_") -> str:
        """
        创建临时文件
        
        Args:
            suffix: 文件后缀
            prefix: 文件前缀
            
        Returns:
            str: 临时文件路径
        """
        return tempfile.mktemp(suffix=suffix, prefix=prefix)
    
    @staticmethod
    def create_temp_directory(prefix: str = "pixiv_") -> str:
        """
        创建临时目录
        
        Args:
            prefix: 目录前缀
            
        Returns:
            str: 临时目录路径
        """
        return tempfile.mkdtemp(prefix=prefix)
    
    @staticmethod
    def read_file_lines(file_path: str, encoding: str = 'utf-8') -> Optional[list]:
        """
        读取文件所有行
        
        Args:
            file_path: 文件路径
            encoding: 文件编码
            
        Returns:
            Optional[list]: 文件行列表，失败返回None
        """
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                return f.readlines()
        except Exception as e:
            logging.error(f"读取文件失败: {file_path}, 错误: {e}")
            return None
    
    @staticmethod
    def write_file_lines(file_path: str, lines: list, encoding: str = 'utf-8') -> bool:
        """
        写入文件行
        
        Args:
            file_path: 文件路径
            lines: 行列表
            encoding: 文件编码
            
        Returns:
            bool: 是否成功
        """
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w', encoding=encoding) as f:
                f.writelines(lines)
            return True
        except Exception as e:
            logging.error(f"写入文件失败: {file_path}, 错误: {e}")
            return False
    
    @staticmethod
    def append_to_file(file_path: str, content: str, encoding: str = 'utf-8') -> bool:
        """
        追加内容到文件
        
        Args:
            file_path: 文件路径
            content: 要追加的内容
            encoding: 文件编码
            
        Returns:
            bool: 是否成功
        """
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'a', encoding=encoding) as f:
                f.write(content)
            return True
        except Exception as e:
            logging.error(f"追加文件失败: {file_path}, 错误: {e}")
            return False
