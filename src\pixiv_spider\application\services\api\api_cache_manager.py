"""
API缓存管理器

专门负责API响应的缓存管理
"""

import logging
import time
import hashlib
from functools import lru_cache
from typing import Dict, Any, Optional, Callable, List

from ....models.config import SpiderConfig


class ApiCacheManager:
    """API缓存管理器 - 专注API响应缓存"""
    
    def __init__(self, spider_config: SpiderConfig):
        """
        初始化API缓存管理器
        
        Args:
            spider_config: 爬虫配置
        """
        self.spider_config = spider_config
        self.logger = logging.getLogger(__name__)
        
        # 缓存统计
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'total_requests': 0,
            'cache_size': 0
        }
        
        # 设置缓存
        self._setup_cache()
    
    def _setup_cache(self) -> None:
        """设置缓存"""
        # 使用lru_cache装饰器创建缓存函数
        self.cached_call = lru_cache(maxsize=self.spider_config.api_cache_size)(self._cache_wrapper)
        self.logger.info(f"📦 初始化API缓存，大小: {self.spider_config.api_cache_size}")
    
    def _cache_wrapper(self, cache_key: str, request_func: Callable, *args, **kwargs) -> Optional[Dict[str, Any]]:
        """
        缓存包装器
        
        Args:
            cache_key: 缓存键
            request_func: 请求函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            Optional[Dict[str, Any]]: API响应数据
        """
        # 这个方法实际上不会被直接调用，只是为了创建缓存函数
        return request_func(*args, **kwargs)
    
    def get_cached_response(self, url: str, request_func: Callable, *args, **kwargs) -> Optional[Dict[str, Any]]:
        """
        获取缓存的响应或执行新请求
        
        Args:
            url: 请求URL
            request_func: 请求函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            Optional[Dict[str, Any]]: API响应数据
        """
        # 生成缓存键
        cache_key = self._generate_cache_key(url, *args, **kwargs)
        
        # 更新统计
        self.cache_stats['total_requests'] += 1
        
        try:
            # 检查缓存
            if self._is_cache_hit(cache_key):
                self.cache_stats['hits'] += 1
                self.logger.debug(f"📦 缓存命中: {url}")
                return self.cached_call(cache_key, request_func, *args, **kwargs)
            else:
                self.cache_stats['misses'] += 1
                self.logger.debug(f"🔍 缓存未命中: {url}")
                result = request_func(*args, **kwargs)
                
                # 如果请求成功，将结果存入缓存
                if result is not None:
                    self.cached_call(cache_key, lambda: result)
                
                return result
        
        except Exception as e:
            self.logger.error(f"❌ 缓存操作失败: {url}, 错误: {e}")
            # 如果缓存操作失败，直接执行请求
            return request_func(*args, **kwargs)
    
    def _generate_cache_key(self, url: str, *args, **kwargs) -> str:
        """
        生成缓存键
        
        Args:
            url: 请求URL
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            str: 缓存键
        """
        # 创建包含URL和参数的字符串
        key_parts = [url]
        
        # 添加位置参数
        if args:
            key_parts.extend(str(arg) for arg in args)
        
        # 添加关键字参数（排序以确保一致性）
        if kwargs:
            sorted_kwargs = sorted(kwargs.items())
            key_parts.extend(f"{k}={v}" for k, v in sorted_kwargs)
        
        # 生成MD5哈希作为缓存键
        key_string = "|".join(key_parts)
        return hashlib.md5(key_string.encode('utf-8')).hexdigest()
    
    def _is_cache_hit(self, cache_key: str) -> bool:
        """
        检查是否缓存命中
        
        Args:
            cache_key: 缓存键
            
        Returns:
            bool: 是否命中缓存
        """
        try:
            # 通过检查缓存信息来判断是否命中
            cache_info = self.cached_call.cache_info()
            return cache_info.currsize > 0
        except Exception:
            return False
    
    def should_cache_response(self, url: str, response: Optional[Dict[str, Any]]) -> bool:
        """
        判断响应是否应该被缓存
        
        Args:
            url: 请求URL
            response: API响应
            
        Returns:
            bool: 是否应该缓存
        """
        # 不缓存空响应
        if response is None:
            return False
        
        # 不缓存错误响应
        if isinstance(response, dict) and response.get('error'):
            return False
        
        # 某些API端点不适合缓存（如实时数据）
        no_cache_patterns = [
            '/ranking.php',  # 排行榜数据
            '/search/',      # 搜索结果
            '/ajax/user/',   # 用户数据（可能变化）
        ]
        
        for pattern in no_cache_patterns:
            if pattern in url:
                self.logger.debug(f"🚫 跳过缓存（匹配无缓存模式）: {url}")
                return False
        
        return True
    
    def invalidate_cache(self, pattern: Optional[str] = None) -> None:
        """
        使缓存失效
        
        Args:
            pattern: 可选的URL模式，如果提供则只清除匹配的缓存
        """
        if pattern:
            # 目前lru_cache不支持部分清除，只能全部清除
            self.logger.warning(f"⚠️ lru_cache不支持模式清除，将清除所有缓存: {pattern}")
        
        self.clear_cache()
    
    def clear_cache(self) -> None:
        """清空所有缓存"""
        try:
            self.cached_call.cache_clear()
            self.cache_stats['cache_size'] = 0
            self.logger.info("🗑️ API缓存已清空")
        except Exception as e:
            self.logger.error(f"❌ 清空缓存失败: {e}")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存信息
        
        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        try:
            cache_info = self.cached_call.cache_info()
            
            # 计算命中率
            total_requests = self.cache_stats['total_requests']
            hit_rate = (self.cache_stats['hits'] / total_requests) if total_requests > 0 else 0
            
            return {
                'hits': cache_info.hits,
                'misses': cache_info.misses,
                'maxsize': cache_info.maxsize,
                'currsize': cache_info.currsize,
                'hit_rate': hit_rate,
                'total_requests': total_requests,
                'cache_efficiency': hit_rate * 100  # 百分比形式的命中率
            }
        
        except Exception as e:
            self.logger.error(f"❌ 获取缓存信息失败: {e}")
            return {
                'hits': 0,
                'misses': 0,
                'maxsize': self.spider_config.api_cache_size,
                'currsize': 0,
                'hit_rate': 0,
                'total_requests': self.cache_stats['total_requests'],
                'cache_efficiency': 0
            }
    
    def optimize_cache(self) -> None:
        """优化缓存性能"""
        cache_info = self.get_cache_info()
        
        # 如果命中率太低，考虑调整缓存策略
        if cache_info['hit_rate'] < 0.3 and cache_info['total_requests'] > 100:
            self.logger.warning(f"⚠️ 缓存命中率较低: {cache_info['hit_rate']:.2%}")
            self.logger.info("💡 建议检查API调用模式或调整缓存大小")
        
        # 如果缓存使用率很高，建议增加缓存大小
        if cache_info['currsize'] >= cache_info['maxsize'] * 0.9:
            self.logger.info(f"📈 缓存使用率较高: {cache_info['currsize']}/{cache_info['maxsize']}")
            self.logger.info("💡 建议增加缓存大小以提高性能")
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """
        获取详细的缓存统计信息
        
        Returns:
            Dict[str, Any]: 详细统计信息
        """
        cache_info = self.get_cache_info()
        
        return {
            'basic_stats': cache_info,
            'performance_metrics': {
                'memory_efficiency': cache_info['currsize'] / cache_info['maxsize'] if cache_info['maxsize'] > 0 else 0,
                'request_reduction': cache_info['hits'] / (cache_info['hits'] + cache_info['misses']) if (cache_info['hits'] + cache_info['misses']) > 0 else 0,
                'cache_utilization': cache_info['currsize'] / cache_info['maxsize'] if cache_info['maxsize'] > 0 else 0
            },
            'recommendations': self._get_cache_recommendations(cache_info)
        }
    
    def _get_cache_recommendations(self, cache_info: Dict[str, Any]) -> List[str]:
        """
        获取缓存优化建议
        
        Args:
            cache_info: 缓存信息
            
        Returns:
            List[str]: 优化建议列表
        """
        recommendations = []
        
        hit_rate = cache_info.get('hit_rate', 0)
        utilization = cache_info.get('currsize', 0) / cache_info.get('maxsize', 1)
        
        if hit_rate < 0.2:
            recommendations.append("缓存命中率过低，考虑检查API调用模式")
        elif hit_rate < 0.5:
            recommendations.append("缓存命中率偏低，可以优化API调用顺序")
        
        if utilization > 0.9:
            recommendations.append("缓存使用率很高，建议增加缓存大小")
        elif utilization < 0.3:
            recommendations.append("缓存使用率较低，可以考虑减少缓存大小")
        
        if not recommendations:
            recommendations.append("缓存性能良好，无需调整")
        
        return recommendations
