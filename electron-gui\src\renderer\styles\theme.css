/* Pixiv Spider v6.1 现代化主题样式 */

:root {
  /* 浅色主题 - 现代化配色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-quaternary: #e2e8f0;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-muted: #94a3b8;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --border-lighter: #f8fafc;

  /* 现代化阴影系统 */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* 现代化品牌色 - Pixiv风格 */
  --primary-color: #0096fa;
  --primary-hover: #0084e6;
  --primary-active: #0072cc;
  --primary-light: #e6f4ff;
  --success-color: #10b981;
  --success-hover: #059669;
  --success-light: #d1fae5;
  --warning-color: #f59e0b;
  --warning-hover: #d97706;
  --warning-light: #fef3c7;
  --danger-color: #ef4444;
  --danger-hover: #dc2626;
  --danger-light: #fee2e2;
  --error-color: #ef4444;
  --info-color: #6b7280;

  /* 渐变色 */
  --gradient-primary: linear-gradient(135deg, #0096fa 0%, #0084e6 100%);
  --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);

  /* 圆角系统 */
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-2xl: 16px;
  --radius-3xl: 24px;
  --radius-full: 9999px;

  /* 阴影 */
  --shadow: var(--shadow-md);
}

/* 深色主题 - 现代化配色 */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-quaternary: #475569;
  --text-primary: #f8fafc;
  --text-secondary: #e2e8f0;
  --text-tertiary: #cbd5e1;
  --text-muted: #94a3b8;
  --border-color: #334155;
  --border-light: #475569;
  --border-lighter: #64748b;

  /* 深色主题阴影 */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.3);
  --shadow: var(--shadow-md);
}

/* 深色主题类名兼容 */
.dark-theme {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-quaternary: #475569;
  --text-primary: #f8fafc;
  --text-secondary: #e2e8f0;
  --text-tertiary: #cbd5e1;
  --text-muted: #94a3b8;
  --border-color: #334155;
  --border-light: #475569;
  --border-lighter: #64748b;

  /* 深色主题阴影 */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.3);
  --shadow: var(--shadow-md);
}

/* 全局样式 - 现代化字体和基础样式 */
* {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  box-sizing: border-box;
}

body {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  color: var(--text-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', sans-serif;
  line-height: 1.6;
  font-size: 14px;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Element Plus 组件现代化主题覆盖 */

/* 卡片组件 */
.el-card {
  background-color: var(--bg-primary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-sm) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  overflow: hidden !important;
}

.el-card:hover {
  box-shadow: var(--shadow-lg) !important;
  transform: translateY(-2px) !important;
}

.el-card__header {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  border-bottom: 1px solid var(--border-color) !important;
  padding: 20px !important;
  font-weight: 600 !important;
}

.el-card__body {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  padding: 20px !important;
}

/* 按钮组件 */
.el-button {
  border-radius: var(--radius-lg) !important;
  font-weight: 500 !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: var(--shadow-xs) !important;
}

.el-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-md) !important;
}

.el-button--primary {
  background: var(--gradient-primary) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
}

.el-button--primary:hover {
  background: var(--primary-hover) !important;
  border-color: var(--primary-hover) !important;
}

.el-button--success {
  background: var(--gradient-success) !important;
  border-color: var(--success-color) !important;
}

.el-button--warning {
  background: var(--gradient-warning) !important;
  border-color: var(--warning-color) !important;
}

.el-button--danger {
  background: var(--gradient-danger) !important;
  border-color: var(--danger-color) !important;
}

.el-button--default,
.el-button--text {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

/* 输入组件 */
.el-input__wrapper,
.el-input-number__wrapper,
.el-textarea__inner {
  background-color: var(--bg-primary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: var(--radius-lg) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: var(--shadow-xs) !important;
}

.el-input__wrapper:hover,
.el-input-number__wrapper:hover,
.el-textarea__inner:hover {
  border-color: var(--primary-color) !important;
  box-shadow: var(--shadow-sm) !important;
}

.el-input__wrapper.is-focus,
.el-input-number__wrapper.is-focus,
.el-textarea__inner:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px var(--primary-light) !important;
}

.el-input__inner,
.el-input-number__inner {
  color: var(--text-primary) !important;
  background-color: transparent !important;
}

/* 选择器组件 */
.el-select .el-input__wrapper,
.el-select__wrapper {
  background-color: var(--bg-primary) !important;
  border-color: var(--border-color) !important;
}

.el-select-dropdown {
  background-color: var(--bg-primary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-lg) !important;
}

.el-select-dropdown__item {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.el-select-dropdown__item:hover {
  background-color: var(--bg-tertiary) !important;
}

/* 表格组件 */
.el-table {
  background-color: var(--bg-primary) !important;
  border-radius: var(--radius-lg) !important;
  overflow: hidden !important;
  box-shadow: var(--shadow-sm) !important;
}

.el-table__header,
.el-table__body {
  background-color: var(--bg-primary) !important;
}

.el-table th,
.el-table td {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

.el-table__row:hover {
  background-color: var(--bg-tertiary) !important;
}

/* 对话框组件 */
.el-dialog {
  background-color: var(--bg-primary) !important;
  border-radius: var(--radius-2xl) !important;
  box-shadow: var(--shadow-2xl) !important;
  border: none !important;
}

.el-dialog__header {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  border-bottom: 1px solid var(--border-color) !important;
  padding: 24px !important;
}

.el-dialog__body {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  padding: 24px !important;
}

/* 进度条组件 */
.el-progress-bar__outer {
  background-color: var(--bg-tertiary) !important;
  border-radius: var(--radius-full) !important;
  overflow: hidden !important;
}

.el-progress-bar__inner {
  background: var(--gradient-primary) !important;
  border-radius: var(--radius-full) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 开关组件 */
.el-switch__core {
  background-color: var(--bg-tertiary) !important;
  border-radius: var(--radius-full) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.el-switch.is-checked .el-switch__core {
  background-color: var(--primary-color) !important;
}

/* 滑块组件 */
.el-slider__runway {
  background-color: var(--bg-tertiary) !important;
  border-radius: var(--radius-full) !important;
}

.el-slider__bar {
  background: var(--gradient-primary) !important;
  border-radius: var(--radius-full) !important;
}

/* 描述列表组件 */
.el-descriptions {
  background-color: var(--bg-primary) !important;
  border-radius: var(--radius-lg) !important;
  overflow: hidden !important;
}

.el-descriptions__header {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  padding: 20px !important;
}

.el-descriptions__title {
  color: var(--text-primary) !important;
  font-weight: 600 !important;
}

.el-descriptions__body {
  background-color: var(--bg-primary) !important;
}

.el-descriptions-item__label,
.el-descriptions-item__content {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
  padding: 12px 16px !important;
}

.el-descriptions-item__label {
  font-weight: 500 !important;
  color: var(--text-secondary) !important;
}

/* 分割线组件 */
.el-divider {
  border-color: var(--border-color) !important;
  margin: 24px 0 !important;
}

.el-divider__text {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  font-weight: 500 !important;
  padding: 0 16px !important;
}

/* 标签组件 */
.el-tag {
  border-radius: var(--radius-md) !important;
  font-weight: 500 !important;
  padding: 4px 12px !important;
  border: none !important;
}

.el-tag--primary {
  background-color: var(--primary-light) !important;
  color: var(--primary-color) !important;
}

.el-tag--success {
  background-color: var(--success-light) !important;
  color: var(--success-color) !important;
}

.el-tag--warning {
  background-color: var(--warning-light) !important;
  color: var(--warning-color) !important;
}

.el-tag--danger {
  background-color: var(--danger-light) !important;
  color: var(--danger-color) !important;
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6,
.title,
.subtitle,
.header-title,
.card-title {
  color: var(--text-primary) !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin: 0 0 16px 0 !important;
}

h1, .title {
  font-size: 2rem !important;
  font-weight: 700 !important;
}

h2, .subtitle {
  font-size: 1.5rem !important;
}

h3, .header-title {
  font-size: 1.25rem !important;
}

h4, .card-title {
  font-size: 1.125rem !important;
}

/* 表单组件 */
.el-form-item__label {
  color: var(--text-secondary) !important;
  font-weight: 500 !important;
  line-height: 1.5 !important;
}

.el-checkbox__inner,
.el-radio__inner {
  background-color: var(--bg-primary) !important;
  border-color: var(--border-color) !important;
  border-radius: var(--radius-sm) !important;
}

.el-checkbox__inner:hover,
.el-radio__inner:hover {
  border-color: var(--primary-color) !important;
}

.el-checkbox.is-checked .el-checkbox__inner,
.el-radio.is-checked .el-radio__inner {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

/* 分页组件 */
.el-pagination {
  background-color: var(--bg-primary) !important;
}

.el-pager li {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  border-radius: var(--radius-md) !important;
  margin: 0 2px !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.el-pager li:hover {
  background-color: var(--bg-tertiary) !important;
  transform: translateY(-1px) !important;
}

.el-pager li.is-active {
  background-color: var(--primary-color) !important;
  color: white !important;
}

/* 消息和通知组件 */
.el-alert {
  border-radius: var(--radius-lg) !important;
  border: none !important;
  box-shadow: var(--shadow-sm) !important;
}

.el-message {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-lg) !important;
  border: none !important;
}

.el-notification {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
  border: none !important;
}

/* 下拉菜单组件 */
.el-dropdown-menu {
  background-color: var(--bg-primary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-lg) !important;
}

.el-dropdown-menu__item {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.el-dropdown-menu__item:hover {
  background-color: var(--bg-tertiary) !important;
}

/* 菜单组件 */
.el-menu {
  background-color: var(--bg-primary) !important;
  border-radius: var(--radius-lg) !important;
}

.el-menu-item,
.el-submenu__title {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  border-radius: var(--radius-md) !important;
  margin: 2px 8px !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.el-menu-item:hover,
.el-submenu__title:hover {
  background-color: var(--bg-tertiary) !important;
}

.el-menu-item.is-active {
  background-color: var(--primary-light) !important;
  color: var(--primary-color) !important;
}

/* 自定义组件现代化样式 */
.login-card {
  margin-bottom: 24px;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-md) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.login-card:hover {
  box-shadow: var(--shadow-lg) !important;
  transform: translateY(-2px) !important;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  font-size: 16px;
  color: var(--text-primary);
}

.card-header .el-icon {
  font-size: 18px;
  color: var(--primary-color);
}

.status-display {
  text-align: center;
  margin: 20px 0;
  padding: 16px;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.login-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;
}

.login-actions .el-button {
  flex: 1;
  min-height: 40px;
  font-weight: 500;
}

/* 登录对话框现代化样式 */
.login-dialog {
  padding: 24px 0;
}

.status-section {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-color);
}

.status-icon {
  font-size: 56px;
  margin-right: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
  border-radius: var(--radius-full);
  color: white;
  box-shadow: var(--shadow-lg);
}

.rotating {
  animation: rotate 2s linear infinite;
}

.pulse {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.status-text h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  animation: fadeInUp 0.5s ease-out;
}

.status-text p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.6;
  animation: fadeInUp 0.5s ease-out 0.1s both;
}

.progress-bar {
  margin: 24px 0;
  animation: fadeInUp 0.5s ease-out 0.2s both;
}

.login-steps {
  margin: 24px 0;
  padding: 20px;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.steps-list {
  margin: 12px 0 0 0;
  padding-left: 24px;
}

.steps-list li {
  margin: 12px 0;
  line-height: 1.6;
  color: var(--text-secondary);
  position: relative;
}

.steps-list li::marker {
  color: var(--primary-color);
  font-weight: 600;
}

.error-alert {
  margin-top: 24px;
  border-radius: var(--radius-lg) !important;
  animation: fadeInUp 0.5s ease-out;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

/* 现代化响应式设计 */
@media (max-width: 1024px) {
  .card-header {
    font-size: 15px;
  }

  .status-icon {
    font-size: 48px;
    width: 70px;
    height: 70px;
  }
}

@media (max-width: 768px) {
  .login-actions {
    flex-direction: column;
    gap: 8px;
  }

  .dialog-footer {
    flex-direction: column;
    gap: 8px;
  }

  .status-section {
    flex-direction: column;
    text-align: center;
    padding: 16px;
  }

  .status-icon {
    margin-right: 0;
    margin-bottom: 16px;
    font-size: 40px;
    width: 60px;
    height: 60px;
  }

  .card-header {
    font-size: 14px;
  }

  .card-header .el-icon {
    font-size: 16px;
  }

  .status-text h3 {
    font-size: 18px;
  }

  .steps-list {
    padding-left: 20px;
  }
}

@media (max-width: 480px) {
  .status-section {
    padding: 12px;
  }

  .login-steps {
    padding: 16px;
  }

  .status-text h3 {
    font-size: 16px;
  }

  .status-text p {
    font-size: 13px;
  }
}

/* 现代化滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--radius-full);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
  transform: scaleY(1.2);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* 现代化过渡动画系统 */
* {
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 页面加载动画 */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 组件入场动画 */
.el-card {
  animation: slideInFromTop 0.5s ease-out;
}

.config-panel .el-card:nth-child(1) {
  animation-delay: 0.1s;
  animation-fill-mode: both;
}

.config-panel .el-card:nth-child(2) {
  animation-delay: 0.2s;
  animation-fill-mode: both;
}

.config-panel .el-card:nth-child(3) {
  animation-delay: 0.3s;
  animation-fill-mode: both;
}

.config-panel .el-card:nth-child(4) {
  animation-delay: 0.4s;
  animation-fill-mode: both;
}

/* 焦点样式 */
.el-button:focus-visible,
.el-input__wrapper:focus-within,
.el-select:focus-within .el-input__wrapper {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* 禁用状态样式 */
.el-button:disabled,
.el-input:disabled .el-input__wrapper,
.el-select:disabled .el-input__wrapper {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}
