"""
目录管理工具

提供目录操作和管理功能
"""

import os
import shutil
from pathlib import Path
from typing import List, Optional
import logging


class DirectoryManager:
    """目录管理工具类"""
    
    @staticmethod
    def ensure_directory(path: str) -> bool:
        """
        确保目录存在
        
        Args:
            path: 目录路径
            
        Returns:
            bool: 是否成功
        """
        try:
            Path(path).mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            logging.error(f"创建目录失败: {path}, 错误: {e}")
            return False
    
    @staticmethod
    def create_directory(path: str, exist_ok: bool = True) -> bool:
        """
        创建目录
        
        Args:
            path: 目录路径
            exist_ok: 如果目录已存在是否报错
            
        Returns:
            bool: 是否成功
        """
        try:
            os.makedirs(path, exist_ok=exist_ok)
            return True
        except Exception as e:
            logging.error(f"创建目录失败: {path}, 错误: {e}")
            return False
    
    @staticmethod
    def remove_directory(path: str, force: bool = False) -> bool:
        """
        删除目录
        
        Args:
            path: 目录路径
            force: 是否强制删除（包含文件的目录）
            
        Returns:
            bool: 是否成功
        """
        try:
            if not os.path.exists(path):
                return True
            
            if force:
                shutil.rmtree(path)
            else:
                os.rmdir(path)  # 只能删除空目录
            return True
        except Exception as e:
            logging.error(f"删除目录失败: {path}, 错误: {e}")
            return False
    
    @staticmethod
    def copy_directory(src: str, dst: str) -> bool:
        """
        复制目录
        
        Args:
            src: 源目录
            dst: 目标目录
            
        Returns:
            bool: 是否成功
        """
        try:
            shutil.copytree(src, dst)
            return True
        except Exception as e:
            logging.error(f"复制目录失败: {src} -> {dst}, 错误: {e}")
            return False
    
    @staticmethod
    def move_directory(src: str, dst: str) -> bool:
        """
        移动目录
        
        Args:
            src: 源目录
            dst: 目标目录
            
        Returns:
            bool: 是否成功
        """
        try:
            shutil.move(src, dst)
            return True
        except Exception as e:
            logging.error(f"移动目录失败: {src} -> {dst}, 错误: {e}")
            return False
    
    @staticmethod
    def list_directories(path: str, recursive: bool = False) -> List[str]:
        """
        列出目录中的子目录
        
        Args:
            path: 目录路径
            recursive: 是否递归列出
            
        Returns:
            List[str]: 子目录列表
        """
        try:
            directories = []
            
            if recursive:
                for root, dirs, _ in os.walk(path):
                    for dir_name in dirs:
                        directories.append(os.path.join(root, dir_name))
            else:
                for item in os.listdir(path):
                    item_path = os.path.join(path, item)
                    if os.path.isdir(item_path):
                        directories.append(item_path)
            
            return directories
        except Exception as e:
            logging.error(f"列出目录失败: {path}, 错误: {e}")
            return []
    
    @staticmethod
    def list_files(path: str, recursive: bool = False, extensions: Optional[List[str]] = None) -> List[str]:
        """
        列出目录中的文件
        
        Args:
            path: 目录路径
            recursive: 是否递归列出
            extensions: 文件扩展名过滤器
            
        Returns:
            List[str]: 文件列表
        """
        try:
            files = []
            
            if recursive:
                for root, _, file_names in os.walk(path):
                    for file_name in file_names:
                        file_path = os.path.join(root, file_name)
                        if DirectoryManager._match_extension(file_name, extensions):
                            files.append(file_path)
            else:
                for item in os.listdir(path):
                    item_path = os.path.join(path, item)
                    if os.path.isfile(item_path):
                        if DirectoryManager._match_extension(item, extensions):
                            files.append(item_path)
            
            return files
        except Exception as e:
            logging.error(f"列出文件失败: {path}, 错误: {e}")
            return []
    
    @staticmethod
    def _match_extension(filename: str, extensions: Optional[List[str]]) -> bool:
        """检查文件扩展名是否匹配"""
        if not extensions:
            return True
        
        file_ext = os.path.splitext(filename)[1].lower()
        return any(file_ext == ext.lower() if ext.startswith('.') else file_ext == f'.{ext.lower()}' 
                  for ext in extensions)
    
    @staticmethod
    def get_directory_size(path: str) -> int:
        """
        获取目录大小
        
        Args:
            path: 目录路径
            
        Returns:
            int: 目录大小（字节）
        """
        try:
            total_size = 0
            for root, _, files in os.walk(path):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        total_size += os.path.getsize(file_path)
                    except (OSError, FileNotFoundError):
                        continue
            return total_size
        except Exception as e:
            logging.error(f"获取目录大小失败: {path}, 错误: {e}")
            return 0
    
    @staticmethod
    def clean_empty_directories(root_path: str) -> int:
        """
        清理空目录
        
        Args:
            root_path: 根目录路径
            
        Returns:
            int: 删除的目录数量
        """
        deleted_count = 0
        
        try:
            for root, dirs, files in os.walk(root_path, topdown=False):
                for dir_name in dirs:
                    dir_path = os.path.join(root, dir_name)
                    try:
                        if not os.listdir(dir_path):  # 目录为空
                            os.rmdir(dir_path)
                            deleted_count += 1
                    except OSError:
                        continue
                        
        except Exception as e:
            logging.error(f"清理空目录失败: {root_path}, 错误: {e}")
        
        return deleted_count
    
    @staticmethod
    def is_empty(path: str) -> bool:
        """
        检查目录是否为空
        
        Args:
            path: 目录路径
            
        Returns:
            bool: 是否为空
        """
        try:
            return len(os.listdir(path)) == 0
        except Exception:
            return False
    
    @staticmethod
    def get_directory_info(path: str) -> dict:
        """
        获取目录信息
        
        Args:
            path: 目录路径
            
        Returns:
            dict: 目录信息
        """
        try:
            if not os.path.exists(path):
                return {'exists': False}
            
            files = DirectoryManager.list_files(path, recursive=True)
            directories = DirectoryManager.list_directories(path, recursive=True)
            
            return {
                'exists': True,
                'is_directory': os.path.isdir(path),
                'size_bytes': DirectoryManager.get_directory_size(path),
                'file_count': len(files),
                'directory_count': len(directories),
                'is_empty': DirectoryManager.is_empty(path)
            }
        except Exception as e:
            logging.error(f"获取目录信息失败: {path}, 错误: {e}")
            return {'exists': False, 'error': str(e)}
