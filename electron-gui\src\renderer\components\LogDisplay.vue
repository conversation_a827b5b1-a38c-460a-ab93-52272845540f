<template>
  <el-card class="log-card modern-card modern-log" shadow="hover">
    <template #header>
      <div class="card-header log-header">
        <div class="header-left">
          <el-icon class="header-icon"><Document /></el-icon>
          <span class="header-title">运行日志</span>
        </div>
        <div class="header-actions">
          <el-button-group size="small" class="modern-button-group">
            <el-button @click="scrollToBottom" :disabled="logs.length === 0">
              <el-icon><Bottom /></el-icon>
              底部
            </el-button>
            <el-button @click="exportLogs" :disabled="logs.length === 0">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
            <el-button @click="clearLogs" :disabled="logs.length === 0">
              <el-icon><Delete /></el-icon>
              清空
            </el-button>
          </el-button-group>
        </div>
      </div>
    </template>
    
    <div class="log-content">
      <!-- 日志过滤器 -->
      <div class="log-filters">
        <el-input
          v-model="searchText"
          placeholder="搜索日志..."
          clearable
          size="small"
          style="width: 200px"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select v-model="filterLevel" placeholder="日志级别" size="small" style="width: 120px">
          <el-option label="全部" value="all" />
          <el-option label="信息" value="info" />
          <el-option label="警告" value="warning" />
          <el-option label="错误" value="error" />
          <el-option label="成功" value="success" />
        </el-select>
        
        <el-switch
          v-model="autoScroll"
          active-text="自动滚动"
          inactive-text=""
          size="small"
        />
      </div>
      
      <!-- 日志列表 -->
      <div class="log-container" ref="logContainer">
        <el-scrollbar ref="scrollbar" height="400px">
          <div class="log-list">
            <div
              v-for="log in filteredLogs"
              :key="log.id"
              :class="['log-item', `log-${getLogType(log.message)}`]"
            >
              <div class="log-time">{{ log.timestamp }}</div>
              <div class="log-message">
                <el-icon :class="`log-icon-${getLogType(log.message)}`">
                  <component :is="getLogIcon(log.message)" />
                </el-icon>
                <span class="log-text">{{ log.message }}</span>
              </div>
            </div>
            
            <!-- 空状态 -->
            <div v-if="filteredLogs.length === 0" class="empty-logs">
              <el-empty description="暂无日志" :image-size="80">
                <template #image>
                  <el-icon size="80" :style="{ color: 'var(--text-muted)' }"><Document /></el-icon>
                </template>
              </el-empty>
            </div>
          </div>
        </el-scrollbar>
      </div>
      
      <!-- 日志统计 -->
      <div class="log-stats">
        <el-text size="small" type="info">
          总计: {{ logs.length }} 条 | 
          显示: {{ filteredLogs.length }} 条 |
          最后更新: {{ lastUpdateTime }}
        </el-text>
      </div>
    </div>
  </el-card>
</template>

<script>
import { mapState } from 'vuex'
import { 
  Document, 
  Bottom, 
  Download, 
  Delete, 
  Search,
  InfoFilled,
  WarningFilled,
  CircleCloseFilled,
  SuccessFilled,
  QuestionFilled
} from '@element-plus/icons-vue'

export default {
  name: 'LogDisplay',
  components: {
    Document,
    Bottom,
    Download,
    Delete,
    Search,
    InfoFilled,
    WarningFilled,
    CircleCloseFilled,
    SuccessFilled,
    QuestionFilled
  },
  data() {
    return {
      searchText: '',
      filterLevel: 'all',
      autoScroll: true,
      lastUpdateTime: ''
    }
  },
  computed: {
    ...mapState(['logs']),
    
    filteredLogs() {
      let filtered = this.logs
      
      // 文本搜索
      if (this.searchText) {
        filtered = filtered.filter(log => 
          log.message.toLowerCase().includes(this.searchText.toLowerCase())
        )
      }
      
      // 级别过滤
      if (this.filterLevel !== 'all') {
        filtered = filtered.filter(log => 
          this.getLogType(log.message) === this.filterLevel
        )
      }
      
      return filtered
    }
  },
  
  methods: {
    getLogType(message) {
      // 优化中文日志类型识别
      const lowerMessage = message.toLowerCase()

      // 错误类型
      if (message.includes('❌') ||
          message.includes('错误') ||
          message.includes('失败') ||
          message.includes('ERROR') ||
          message.includes('CRITICAL') ||
          lowerMessage.includes('error') ||
          lowerMessage.includes('critical') ||
          message.includes('Python Backend Error')) {
        return 'error'
      }
      // 警告类型
      if (message.includes('⚠️') ||
          message.includes('警告') ||
          message.includes('WARNING') ||
          lowerMessage.includes('warning') ||
          message.includes('Python Backend Warning')) {
        return 'warning'
      }
      // 成功类型
      if (message.includes('✅') ||
          message.includes('成功') ||
          message.includes('完成') ||
          message.includes('已完成') ||
          message.includes('SUCCESS') ||
          lowerMessage.includes('success')) {
        return 'success'
      }
      // 默认信息类型
      return 'info'
    },
    
    getLogIcon(message) {
      const type = this.getLogType(message)
      switch (type) {
        case 'error': return CircleCloseFilled
        case 'warning': return WarningFilled
        case 'success': return SuccessFilled
        case 'info': return InfoFilled
        default: return QuestionFilled
      }
    },
    
    scrollToBottom() {
      this.$nextTick(() => {
        const scrollbar = this.$refs.scrollbar
        if (scrollbar) {
          scrollbar.setScrollTop(scrollbar.wrapRef.scrollHeight)
        }
      })
    },
    
    clearLogs() {
      this.$store.commit('clearLogs')
      this.lastUpdateTime = new Date().toLocaleTimeString()
    },
    
    async exportLogs() {
      try {
        const logText = this.filteredLogs
          .map(log => `[${log.timestamp}] ${log.message}`)
          .join('\n')
        
        const result = await window.electronAPI.showSaveDialog({
          title: '导出日志',
          defaultPath: `pixiv-spider-logs-${new Date().toISOString().split('T')[0]}.txt`,
          filters: [
            { name: '文本文件', extensions: ['txt'] },
            { name: '所有文件', extensions: ['*'] }
          ]
        })
        
        if (!result.canceled && result.filePath) {
          // 这里应该调用文件写入API
          this.$message.success('日志导出成功')
        }
      } catch (error) {
        this.$message.error('导出日志失败')
      }
    },
    
    updateLastTime() {
      this.lastUpdateTime = new Date().toLocaleTimeString()
    }
  },
  
  watch: {
    logs: {
      handler(newLogs) {
        this.updateLastTime()
        
        // 自动滚动到底部
        if (this.autoScroll && newLogs.length > 0) {
          this.$nextTick(() => {
            this.scrollToBottom()
          })
        }
      },
      deep: true
    }
  },
  
  mounted() {
    this.updateLastTime()
    
    // 添加一些示例日志
    setTimeout(() => {
      this.$store.commit('addLog', '🚀 Pixiv Spider 启动成功')
      this.$store.commit('addLog', '🔧 正在初始化配置...')
      this.$store.commit('addLog', '✅ 配置加载完成')
    }, 1000)
  }
}
</script>

<style scoped>
.log-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
}

.card-header > div:first-child {
  display: flex;
  align-items: center;
  gap: 8px;
}

.log-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.log-filters {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  padding: 10px;
  background: var(--bg-secondary);
  border-radius: 6px;
}

.log-container {
  flex: 1;
  min-height: 0;
}

.log-list {
  padding: 10px;
}

.log-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 6px;
  border-left: 3px solid transparent;
  transition: all 0.2s;
}

.log-item:hover {
  background: var(--bg-tertiary);
}

.log-item.log-info {
  border-left-color: var(--accent-color);
}

.log-item.log-success {
  border-left-color: var(--success-color);
}

.log-item.log-warning {
  border-left-color: var(--warning-color);
}

.log-item.log-error {
  border-left-color: var(--error-color);
}

.log-time {
  font-size: 12px;
  color: var(--text-muted);
  white-space: nowrap;
  min-width: 80px;
  font-family: 'Courier New', monospace;
}

.log-message {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.log-text {
  flex: 1;
  word-break: break-word;
  word-wrap: break-word;
  line-height: 1.4;
  font-size: 13px;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', 'Segoe UI', Arial, sans-serif;
  white-space: pre-wrap;
}

.log-icon-info {
  color: var(--accent-color);
}

.log-icon-success {
  color: var(--success-color);
}

.log-icon-warning {
  color: var(--warning-color);
}

.log-icon-error {
  color: var(--error-color);
}

.empty-logs {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.log-stats {
  padding: 10px;
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
  text-align: center;
}
</style>
