"""
Pixiv爬虫核心类

重构后的精简设计，采用组件化架构
"""

import logging
from typing import Callable, Dict, Any

from ..models.config import DownloadConfig, SpiderConfig
from ..models.exceptions import PixivSpiderError
from .spider_context import SpiderContext
from .authentication_manager import AuthenticationManager
from .component_manager import ComponentManager
from .download_orchestrator import DownloadOrchestrator


class PixivSpider:
    """Pixiv爬虫主类 - 重构后的组件化设计"""

    def __init__(self, container_or_config=None):
        """
        初始化爬虫

        Args:
            container_or_config: 服务容器或配置管理器，如果为None则创建默认实例
        """
        self.logger = logging.getLogger(__name__)

        # 初始化核心组件
        self.context = SpiderContext(container_or_config)
        self.auth_manager = AuthenticationManager(self.context)
        self.component_manager = ComponentManager(self.context, self.auth_manager)
        self.download_orchestrator = DownloadOrchestrator(
            self.context, self.auth_manager, self.component_manager
        )

        self.logger.info("🚀 Pixiv爬虫初始化完成 - 组件化架构")

    # ===== 回调函数设置 =====

    def set_progress_callback(self, callback: Callable) -> None:
        """设置进度回调函数"""
        self.component_manager.set_progress_callback(callback)

    def set_status_callback(self, callback: Callable) -> None:
        """设置状态回调函数"""
        self.component_manager.set_status_callback(callback)

    # ===== 认证相关方法 =====

    def authenticate(self) -> bool:
        """
        进行身份验证

        Returns:
            bool: 是否验证成功
        """
        return self.auth_manager.authenticate()

    def interactive_login(self) -> bool:
        """
        交互式登录

        Returns:
            bool: 是否登录成功
        """
        return self.auth_manager.interactive_login()

    # ===== 下载相关方法 =====

    def start_download(self) -> Dict[str, Any]:
        """
        开始下载

        Returns:
            Dict[str, Any]: 下载结果统计
        """
        return self.download_orchestrator.start_download()

    def stop_download(self) -> None:
        """停止下载"""
        self.download_orchestrator.stop_download()

    def pause_download(self) -> None:
        """暂停下载"""
        self.download_orchestrator.pause_download()

    def resume_download(self) -> bool:
        """继续下载"""
        return self.download_orchestrator.resume_download()

    # ===== 状态和配置管理 =====

    def get_download_stats(self) -> dict:
        """获取下载统计信息"""
        return {
            "is_running": self.component_manager.resource_manager._is_running,
            "is_authenticated": self.auth_manager.is_authenticated,
            "download_mode": self.context.download_config.download_mode.value,
            "save_path": self.context.download_config.save_path,
            **self.context.get_service_status(),
            **self.auth_manager.get_authentication_status(),
            **self.component_manager.get_component_status()
        }

    def update_download_config(self, config: DownloadConfig) -> None:
        """更新下载配置"""
        self.context.update_download_config(config)

        # 更新路径管理器的配置
        self.component_manager.path_manager.download_config = config

    def update_spider_config(self, config: SpiderConfig) -> None:
        """更新爬虫配置"""
        self.context.update_spider_config(config)

    # ===== 资源管理 =====

    def cleanup(self):
        """清理所有资源"""
        try:
            self.component_manager.cleanup_all_resources()
            self.logger.info("✅ 爬虫资源清理完成")
        except Exception as e:
            self.logger.error(f"❌ 清理资源时出错: {e}")

    def __enter__(self):
        """支持上下文管理器"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """支持上下文管理器"""
        self.cleanup()
        # 不抑制异常
        return False
