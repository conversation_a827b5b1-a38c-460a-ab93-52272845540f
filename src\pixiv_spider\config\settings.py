"""
设置模块 - 重构后的简化版本

此文件现在主要用于向后兼容，实际配置常量已迁移到 config_constants.py
"""

# 向后兼容的导入
from .config_constants import (
    APP_NAME, APP_VERSION, APP_AUTHOR,
    Paths, ApiConstants, NetworkDefaults, PerformanceDefaults,
    SeleniumDefaults, LogDefaults, GuiDefaults, DownloadDefaults,
    FileTypes
)

# 向后兼容的常量映射
DEFAULT_CONFIG_FILE = Paths.CONFIG_FILE
DEFAULT_COOKIES_FILE = Paths.get_cookies_path()
DEFAULT_CACHE_FILE = Paths.CACHE_FILE
DEFAULT_LOG_FILE = Paths.LOG_FILE

DEFAULT_DOWNLOAD_PATH = Paths.DOWNLOAD_DIR
DEFAULT_TEMP_PATH = Paths.TEMP_DIR
DEFAULT_LOG_PATH = Paths.LOG_DIR
DEFAULT_COOKIES_PATH = Paths.COOKIES_DIR
DEFAULT_COOKIE_PATH = Paths.COOKIES_DIR  # 向后兼容

PIXIV_BASE_URL = ApiConstants.PIXIV_BASE_URL
PIXIV_API_BASE_URL = ApiConstants.PIXIV_API_BASE_URL
PIXIV_AJAX_URL = ApiConstants.PIXIV_AJAX_URL

DEFAULT_USER_AGENT = ApiConstants.DEFAULT_USER_AGENT
DEFAULT_REFERER = ApiConstants.DEFAULT_REFERER
DEFAULT_TIMEOUT = NetworkDefaults.TIMEOUT
DEFAULT_RETRY_ATTEMPTS = NetworkDefaults.RETRY_ATTEMPTS
DEFAULT_RETRY_DELAY = NetworkDefaults.RETRY_DELAY

DEFAULT_MAX_WORKERS = PerformanceDefaults.MAX_WORKERS
DEFAULT_CONCURRENT_DOWNLOADS = PerformanceDefaults.CONCURRENT_DOWNLOADS
DEFAULT_API_CACHE_SIZE = PerformanceDefaults.API_CACHE_SIZE
DEFAULT_CACHE_EXPIRE_TIME = PerformanceDefaults.CACHE_EXPIRE_TIME

DEFAULT_SELENIUM_TIMEOUT = SeleniumDefaults.TIMEOUT
DEFAULT_PAGE_LOAD_TIMEOUT = SeleniumDefaults.PAGE_LOAD_TIMEOUT

DEFAULT_LOG_LEVEL = LogDefaults.LEVEL
DEFAULT_LOG_MAX_SIZE = LogDefaults.MAX_SIZE
DEFAULT_LOG_BACKUP_COUNT = LogDefaults.BACKUP_COUNT

DEFAULT_WINDOW_WIDTH = GuiDefaults.WINDOW_WIDTH
DEFAULT_WINDOW_HEIGHT = GuiDefaults.WINDOW_HEIGHT
DEFAULT_WINDOW_MIN_WIDTH = GuiDefaults.MIN_WIDTH
DEFAULT_WINDOW_MIN_HEIGHT = GuiDefaults.MIN_HEIGHT

DEFAULT_START_PAGE = DownloadDefaults.START_PAGE
DEFAULT_END_PAGE = DownloadDefaults.END_PAGE
DEFAULT_DAYS = DownloadDefaults.DAYS
DEFAULT_MIN_BOOKMARKS = DownloadDefaults.MIN_BOOKMARKS
DEFAULT_MAX_BOOKMARKS = DownloadDefaults.MAX_BOOKMARKS

SUPPORTED_IMAGE_EXTENSIONS = FileTypes.IMAGE_EXTENSIONS
SUPPORTED_ANIMATION_EXTENSIONS = FileTypes.ANIMATION_EXTENSIONS
MAX_FILE_SIZE = FileTypes.MAX_FILE_SIZE
MAX_FILENAME_LENGTH = FileTypes.MAX_FILENAME_LENGTH

REQUEST_INTERVAL = NetworkDefaults.REQUEST_INTERVAL

# 保留一些特定的设置（未迁移到constants的）
MAX_REQUESTS_PER_MINUTE = 60
MAX_DOWNLOAD_RETRIES = 3
DOWNLOAD_RETRY_DELAY = 2.0
MAX_API_RETRIES = 5
API_RETRY_DELAY = 1.0

# 废弃警告
import warnings

def _deprecated_warning(old_name: str, new_location: str):
    """发出废弃警告"""
    warnings.warn(
        f"{old_name} 已废弃，请使用 {new_location}",
        DeprecationWarning,
        stacklevel=3
    )

# 可以在这里添加特定常量的废弃警告
# 例如：
# def get_deprecated_constant():
#     _deprecated_warning("OLD_CONSTANT", "config_constants.NewConstant")
#     return "value"
