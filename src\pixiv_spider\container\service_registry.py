"""
简化的服务注册器

负责注册系统中的所有服务，使用简化的注册方式
"""

import logging
from typing import Dict, Any
from .service_container import ServiceContainer, ServiceLifetime
from ..services.auth_service import AuthService
from ..services.pixiv_api_service import PixivApiService
from ..services.download_service import DownloadService
from ..services.cache_service import CacheService


class ServiceRegistry:
    """简化的服务注册器"""

    def __init__(self, container: ServiceContainer):
        """
        初始化服务注册器

        Args:
            container: 服务容器
        """
        self.container = container
        self.logger = logging.getLogger(__name__)

    def register_all_services(self) -> ServiceContainer:
        """
        注册所有服务

        Returns:
            ServiceContainer: 服务容器（支持链式调用）
        """
        return (self.container
                .register_singleton(AuthService, name='auth_service')
                .register_singleton(CacheService, name='cache_service')
                .register_transient(PixivApiService, self._create_api_service, name='api_service')
                .register_transient(DownloadService, self._create_download_service, name='download_service'))

    def _create_api_service(self, container: ServiceContainer) -> PixivApiService:
        """
        创建API服务工厂

        Args:
            container: 服务容器

        Returns:
            PixivApiService: API服务实例
        """
        def api_service_factory(cookies: Dict[str, Any]) -> PixivApiService:
            config_manager = container.get_config_manager()
            return PixivApiService(cookies, config_manager)

        return api_service_factory

    def _create_download_service(self, container: ServiceContainer) -> DownloadService:
        """
        创建下载服务工厂

        Args:
            container: 服务容器

        Returns:
            DownloadService: 下载服务实例
        """
        def download_service_factory(api_service: PixivApiService) -> DownloadService:
            config_manager = container.get_config_manager()
            return DownloadService(api_service, config_manager)

        return download_service_factory

    def create_api_service(self, cookies: Dict[str, Any]) -> PixivApiService:
        """
        创建配置好的API服务

        Args:
            cookies: 认证cookies

        Returns:
            PixivApiService: API服务实例
        """
        factory = self.container.get('api_service')
        return factory(cookies)

    def create_download_service(self, api_service: PixivApiService) -> DownloadService:
        """
        创建配置好的下载服务

        Args:
            api_service: API服务实例

        Returns:
            DownloadService: 下载服务实例
        """
        factory = self.container.get('download_service')
        return factory(api_service)

    def get_auth_service(self) -> AuthService:
        """获取认证服务"""
        return self.container.get('auth_service')

    def get_cache_service(self) -> CacheService:
        """获取缓存服务"""
        return self.container.get('cache_service')


class ServiceBuilder:
    """服务构建器 - 提供流畅的服务配置API"""

    def __init__(self):
        self.container = ServiceContainer()
        self.registry = ServiceRegistry(self.container)

    def with_default_services(self) -> 'ServiceBuilder':
        """注册默认服务"""
        self.registry.register_all_services()
        return self

    def with_custom_config_manager(self, config_manager) -> 'ServiceBuilder':
        """使用自定义配置管理器"""
        self.container.register_instance(type(config_manager), config_manager, 'config_manager')
        return self

    def build(self) -> ServiceContainer:
        """构建服务容器"""
        return self.container
