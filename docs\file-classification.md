# 文件分类功能

## 概述

Pixiv Spider v6.0 支持多种文件分类模式，允许用户根据不同的标准来组织下载的作品文件。该功能已在前端UI中完全集成，用户可以在下载设置中轻松选择分类方式。

## 分类模式

### 1. 按日期分类 (by_date)

按作品的上传日期进行分类，每个日期创建一个独立的文件夹。

**文件夹结构：**
```
下载路径/
├── 2025-01-15/
│   ├── 美丽的风景_画师A_123456/
│   └── 另一个作品_画师B_789012/
├── 2025-01-20/
│   └── 可爱的角色_画师C_345678/
└── 2025-02-01/
    └── 抽象艺术_画师A_901234/
```

**适用场景：**
- 按时间线浏览作品
- 查看特定日期的作品
- 时间序列分析

### 2. 按作者分类 (by_author)

按作品的作者进行分类，每个作者创建一个独立的文件夹。

**文件夹结构：**
```
下载路径/
├── 画师A/
│   ├── 美丽的风景_画师A_123456/
│   └── 抽象艺术_画师A_901234/
├── 画师B/
│   └── 另一个作品_画师B_789012/
└── 画师C/
    └── 可爱的角色_画师C_345678/
```

**适用场景：**
- 按画师整理作品集
- 研究特定画师的作品风格
- 画师作品归档

### 3. 按类型分类 (by_type)

按作品类型进行分类，将插画、漫画等不同类型分开存放。

**文件夹结构：**
```
下载路径/
├── illustrations/
│   ├── 美丽的风景_画师A_123456/
│   ├── 可爱的角色_画师C_345678/
│   └── 抽象艺术_画师A_901234/
└── manga/
    └── 漫画作品_画师B_789012/
```

**适用场景：**
- 区分不同类型的作品
- 专门收集某种类型的作品
- 按内容类型管理

### 4. 平铺结构 (flat)

所有作品直接存放在根目录下，不进行额外的分类。

**文件夹结构：**
```
下载路径/
├── 美丽的风景_画师A_123456/
├── 另一个作品_画师B_789012/
├── 可爱的角色_画师C_345678/
└── 抽象艺术_画师A_901234/
```

**适用场景：**
- 简单的文件管理
- 不需要复杂分类的场景
- 与其他工具的兼容性

## 前端UI集成

### 设置位置

文件分类选项位于下载设置面板中，在下载模式选择器的下方。

### UI组件

```vue
<!-- 文件分类模式 -->
<el-form-item label="文件分类">
  <el-select
    v-model="classifyMode"
    @change="saveSettings"
    style="width: 100%"
  >
    <el-option label="按日期分类" value="by_date" />
    <el-option label="按作者分类" value="by_author" />
    <el-option label="按类型分类" value="by_type" />
    <el-option label="平铺结构" value="flat" />
  </el-select>
</el-form-item>
```

### 配置保存

- 分类模式设置会自动保存到本地存储
- 重启应用后会自动恢复上次的设置
- 所有下载模式共享同一个分类设置

## 后端实现

### 配置模型

```python
class ClassifyMode(Enum):
    """分类模式枚举"""
    BY_DATE = "by_date"
    BY_AUTHOR = "by_author"
    BY_TYPE = "by_type"
    FLAT = "flat"

@dataclass
class DownloadConfig:
    """下载配置数据模型"""
    # ... 其他配置
    classify_mode: ClassifyMode = ClassifyMode.BY_DATE
```

### 路径生成逻辑

```python
def generate_save_path(self, artwork: Artwork) -> str:
    """生成保存路径"""
    # 清理文件名
    safe_title = self._sanitize_filename(artwork.title, 50)
    safe_author = self._sanitize_filename(artwork.author_name, 30)
    folder_name = f"{safe_title}_{safe_author}_{artwork.id}"

    base_path = Path(self.download_config.save_path)

    if self.download_config.classify_mode == ClassifyMode.BY_DATE:
        # 按日期分类
        date_str = artwork.upload_date.strftime('%Y-%m-%d') if artwork.upload_date else 'unknown'
        save_dir = base_path / date_str / folder_name
    elif self.download_config.classify_mode == ClassifyMode.BY_AUTHOR:
        # 按作者分类
        save_dir = base_path / safe_author / folder_name
    elif self.download_config.classify_mode == ClassifyMode.BY_TYPE:
        # 按类型分类
        type_folder = "illustrations" if artwork.type == ArtworkType.ILLUSTRATION else "manga"
        save_dir = base_path / type_folder / folder_name
    else:
        # 平铺结构
        save_dir = base_path / folder_name
    
    return str(save_dir)
```

## 文件名安全处理

系统会自动处理文件名中的非法字符：

- **非法字符**: `<>:"/\|?*` 会被替换为 `_`
- **长度限制**: 标题限制50字符，作者名限制30字符
- **空值处理**: 空标题或作者名会被替换为 `untitled`

### 示例

```
原标题: "包含特殊字符<>:\"/\\|?*的标题"
处理后: "包含特殊字符_________的标题"

原作者: "特殊字符作者<>"
处理后: "特殊字符作者__"
```

## 配置传递流程

1. **前端设置**: 用户在UI中选择分类模式
2. **配置保存**: 设置保存到Vuex store和本地存储
3. **配置传递**: 通过unified-config服务转换为后端配置格式
4. **后端处理**: DownloadService使用配置生成正确的文件路径

## 兼容性

- 该功能向后兼容，默认使用按日期分类
- 现有的下载不会受到影响
- 可以随时更改分类模式，新下载会使用新的分类方式

## 注意事项

1. **路径长度**: Windows系统路径长度限制为260字符，深层分类可能导致路径过长
2. **文件移动**: 更改分类模式不会移动已下载的文件
3. **重复检测**: 系统会根据新的路径结构检测重复下载
4. **权限要求**: 确保下载路径具有写入权限

## 使用建议

- **新用户**: 推荐使用按日期分类，便于时间线浏览
- **画师收集**: 推荐使用按作者分类，便于整理画师作品集
- **类型专门**: 推荐使用按类型分类，便于区分插画和漫画
- **简单管理**: 推荐使用平铺结构，减少文件夹层级
