"""
作品处理器

专门负责作品数据的处理，包括路径生成、元数据保存等功能
"""

import os
import json
import logging
import re
from typing import Optional, Dict, Any
from pathlib import Path

from ....models.artwork import Artwork, ArtworkStatus
from ....models.config import DownloadConfig, ClassifyMode


class ArtworkProcessor:
    """作品处理器 - 专注作品数据处理功能"""
    
    def __init__(self, download_config: DownloadConfig):
        """
        初始化作品处理器
        
        Args:
            download_config: 下载配置
        """
        self.download_config = download_config
        self.logger = logging.getLogger(__name__)
    
    def generate_save_path(self, artwork: Artwork) -> str:
        """
        生成作品保存路径
        
        Args:
            artwork: 作品对象
            
        Returns:
            str: 保存路径
        """
        try:
            # 基础保存路径
            base_path = self.download_config.save_path
            
            # 根据分类模式生成路径
            if self.download_config.classify_mode == ClassifyMode.BY_AUTHOR:
                # 按作者分类
                author_name = self._sanitize_filename(artwork.author_name)
                category_path = os.path.join(base_path, f"{artwork.author_id}_{author_name}")
            
            elif self.download_config.classify_mode == ClassifyMode.BY_TAG:
                # 按标签分类（使用第一个标签）
                if artwork.tags:
                    main_tag = self._sanitize_filename(artwork.tags[0])
                    category_path = os.path.join(base_path, f"tag_{main_tag}")
                else:
                    category_path = os.path.join(base_path, "no_tag")
            
            elif self.download_config.classify_mode == ClassifyMode.BY_DATE:
                # 按日期分类
                if artwork.create_date:
                    date_str = artwork.create_date.strftime("%Y-%m")
                    category_path = os.path.join(base_path, date_str)
                else:
                    category_path = os.path.join(base_path, "unknown_date")
            
            else:
                # 不分类，直接保存到根目录
                category_path = base_path
            
            # 生成作品文件夹名
            title = self._sanitize_filename(artwork.title) if artwork.title else "untitled"
            folder_name = f"{artwork.id}_{title}"
            
            # 限制文件夹名长度
            if len(folder_name) > 100:
                folder_name = f"{artwork.id}_{title[:50]}..."
            
            return os.path.join(category_path, folder_name)
        
        except Exception as e:
            self.logger.error(f"❌ 生成保存路径失败: {artwork.id} - {e}")
            # 回退到简单路径
            return os.path.join(self.download_config.save_path, str(artwork.id))
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        清理文件名，移除不合法字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            str: 清理后的文件名
        """
        if not filename:
            return "unnamed"
        
        # 移除或替换不合法字符
        illegal_chars = r'[<>:"/\\|?*]'
        sanitized = re.sub(illegal_chars, '_', filename)
        
        # 移除连续的空格和下划线
        sanitized = re.sub(r'[_\s]+', '_', sanitized)
        
        # 移除首尾的空格和下划线
        sanitized = sanitized.strip('_ ')
        
        # 确保不为空
        if not sanitized:
            return "unnamed"
        
        return sanitized
    
    def save_artwork_metadata(self, artwork: Artwork, save_path: str) -> bool:
        """
        保存作品元数据
        
        Args:
            artwork: 作品对象
            save_path: 保存路径
            
        Returns:
            bool: 是否保存成功
        """
        try:
            metadata_file = os.path.join(save_path, "metadata.json")
            
            metadata = {
                'id': artwork.id,
                'title': artwork.title,
                'author_id': artwork.author_id,
                'author_name': artwork.author_name,
                'description': artwork.description,
                'tags': artwork.tags,
                'view_count': artwork.view_count,
                'like_count': artwork.like_count,
                'bookmark_count': artwork.bookmark_count,
                'create_date': artwork.create_date.isoformat() if artwork.create_date else None,
                'type': artwork.type.value if artwork.type else None,
                'is_multi_page': artwork.is_multi_page,
                'page_count': artwork.page_count,
                'width': artwork.width,
                'height': artwork.height,
                'url_thumbnail': artwork.url_thumbnail,
                'url_medium': artwork.url_medium,
                'url_large': artwork.url_large,
                'is_r18': artwork.is_r18,
                'is_ai': artwork.is_ai,
                'download_time': artwork.download_time.isoformat() if artwork.download_time else None,
                'local_path': artwork.local_path
            }
            
            # 添加页面信息
            if artwork.pages:
                metadata['pages'] = [
                    {
                        'page_number': page.page_number,
                        'url_original': page.url_original,
                        'url_medium': page.url_medium,
                        'width': page.width,
                        'height': page.height
                    }
                    for page in artwork.pages
                ]
            
            # 添加动图元数据
            if artwork.ugoira_metadata:
                metadata['ugoira_metadata'] = artwork.ugoira_metadata
            
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            self.logger.debug(f"💾 元数据已保存: {metadata_file}")
            return True
        
        except Exception as e:
            self.logger.error(f"❌ 保存元数据失败: {artwork.id} - {e}")
            return False
    
    def load_artwork_metadata(self, save_path: str) -> Optional[Dict[str, Any]]:
        """
        加载作品元数据
        
        Args:
            save_path: 保存路径
            
        Returns:
            Optional[Dict[str, Any]]: 元数据，如果不存在则返回None
        """
        try:
            metadata_file = os.path.join(save_path, "metadata.json")
            
            if not os.path.exists(metadata_file):
                return None
            
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            return metadata
        
        except Exception as e:
            self.logger.error(f"❌ 加载元数据失败: {save_path} - {e}")
            return None
    
    def validate_artwork_folder(self, artwork: Artwork, folder_path: str) -> bool:
        """
        验证作品文件夹的完整性
        
        Args:
            artwork: 作品对象
            folder_path: 文件夹路径
            
        Returns:
            bool: 文件夹是否完整
        """
        try:
            if not os.path.exists(folder_path):
                return False
            
            # 检查是否有图片文件
            image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'}
            image_files = []
            
            for file in os.listdir(folder_path):
                if any(file.lower().endswith(ext) for ext in image_extensions):
                    image_files.append(file)
            
            if not image_files:
                self.logger.warning(f"⚠️ 文件夹中没有图片文件: {folder_path}")
                return False
            
            # 如果是多页作品，检查页面数量
            if artwork.is_multi_page and artwork.page_count:
                expected_pages = artwork.page_count
                actual_pages = len(image_files)
                
                if actual_pages < expected_pages:
                    self.logger.warning(f"⚠️ 页面数量不足: {folder_path} (期望: {expected_pages}, 实际: {actual_pages})")
                    return False
            
            # 检查元数据文件
            metadata_file = os.path.join(folder_path, "metadata.json")
            if self.download_config.save_metadata and not os.path.exists(metadata_file):
                self.logger.warning(f"⚠️ 缺少元数据文件: {metadata_file}")
                # 元数据缺失不算致命错误，只要有图片文件就认为是有效的
            
            return True
        
        except Exception as e:
            self.logger.error(f"❌ 验证作品文件夹失败: {folder_path} - {e}")
            return False
    
    def cleanup_incomplete_download(self, folder_path: str) -> None:
        """
        清理不完整的下载
        
        Args:
            folder_path: 文件夹路径
        """
        try:
            if os.path.exists(folder_path):
                import shutil
                shutil.rmtree(folder_path, ignore_errors=True)
                self.logger.info(f"🗑️ 已清理不完整的下载: {folder_path}")
        
        except Exception as e:
            self.logger.error(f"❌ 清理不完整下载失败: {folder_path} - {e}")
    
    def get_folder_size(self, folder_path: str) -> int:
        """
        获取文件夹大小
        
        Args:
            folder_path: 文件夹路径
            
        Returns:
            int: 文件夹大小（字节）
        """
        try:
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(folder_path):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
            return total_size
        
        except Exception as e:
            self.logger.error(f"❌ 获取文件夹大小失败: {folder_path} - {e}")
            return 0
    
    def create_artwork_summary(self, artwork: Artwork, save_path: str) -> bool:
        """
        创建作品摘要文件
        
        Args:
            artwork: 作品对象
            save_path: 保存路径
            
        Returns:
            bool: 是否创建成功
        """
        try:
            summary_file = os.path.join(save_path, "summary.txt")
            
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(f"作品ID: {artwork.id}\n")
                f.write(f"标题: {artwork.title}\n")
                f.write(f"作者: {artwork.author_name} (ID: {artwork.author_id})\n")
                f.write(f"创建时间: {artwork.create_date}\n")
                f.write(f"标签: {', '.join(artwork.tags) if artwork.tags else '无'}\n")
                f.write(f"浏览数: {artwork.view_count}\n")
                f.write(f"点赞数: {artwork.like_count}\n")
                f.write(f"收藏数: {artwork.bookmark_count}\n")
                f.write(f"是否R18: {'是' if artwork.is_r18 else '否'}\n")
                f.write(f"是否AI作品: {'是' if artwork.is_ai else '否'}\n")
                f.write(f"页面数: {artwork.page_count}\n")
                f.write(f"尺寸: {artwork.width}x{artwork.height}\n")
                
                if artwork.description:
                    f.write(f"\n描述:\n{artwork.description}\n")
            
            self.logger.debug(f"📝 摘要文件已创建: {summary_file}")
            return True
        
        except Exception as e:
            self.logger.error(f"❌ 创建摘要文件失败: {artwork.id} - {e}")
            return False
