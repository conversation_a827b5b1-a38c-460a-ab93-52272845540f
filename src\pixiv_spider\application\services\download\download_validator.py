"""
下载验证器

专门负责验证下载的完整性和正确性
"""

import os
import logging
from typing import Tuple, Optional, List, Dict, Any
from pathlib import Path

from ....models.artwork import Artwork, ArtworkStatus
from ....models.config import DownloadConfig


class DownloadValidator:
    """下载验证器 - 专注下载验证功能"""
    
    def __init__(self, download_config: DownloadConfig):
        """
        初始化下载验证器
        
        Args:
            download_config: 下载配置
        """
        self.download_config = download_config
        self.logger = logging.getLogger(__name__)
    
    def validate_artwork_download(self, artwork: Artwork, save_path: str) -> Tuple[bool, str]:
        """
        验证作品下载的完整性
        
        Args:
            artwork: 作品对象
            save_path: 保存路径
            
        Returns:
            Tuple[bool, str]: (是否有效, 验证信息)
        """
        try:
            if not os.path.exists(save_path):
                return False, f"保存路径不存在: {save_path}"
            
            # 检查基本文件结构
            validation_result = self._validate_folder_structure(save_path)
            if not validation_result[0]:
                return validation_result
            
            # 检查图片文件
            validation_result = self._validate_image_files(artwork, save_path)
            if not validation_result[0]:
                return validation_result
            
            # 检查页面完整性
            if artwork.is_multi_page:
                validation_result = self._validate_multi_page_artwork(artwork, save_path)
                if not validation_result[0]:
                    return validation_result
            
            # 检查元数据文件
            if self.download_config.save_metadata:
                validation_result = self._validate_metadata_file(save_path)
                if not validation_result[0]:
                    return validation_result
            
            return True, "下载验证通过"
        
        except Exception as e:
            self.logger.error(f"❌ 验证下载失败: {artwork.id} - {e}")
            return False, f"验证过程出错: {e}"
    
    def _validate_folder_structure(self, save_path: str) -> Tuple[bool, str]:
        """
        验证文件夹结构
        
        Args:
            save_path: 保存路径
            
        Returns:
            Tuple[bool, str]: (是否有效, 验证信息)
        """
        try:
            if not os.path.isdir(save_path):
                return False, "保存路径不是有效的目录"
            
            # 检查目录是否可读
            if not os.access(save_path, os.R_OK):
                return False, "目录不可读"
            
            # 检查目录是否为空
            files = os.listdir(save_path)
            if not files:
                return False, "目录为空"
            
            return True, "文件夹结构正常"
        
        except Exception as e:
            return False, f"验证文件夹结构失败: {e}"
    
    def _validate_image_files(self, artwork: Artwork, save_path: str) -> Tuple[bool, str]:
        """
        验证图片文件
        
        Args:
            artwork: 作品对象
            save_path: 保存路径
            
        Returns:
            Tuple[bool, str]: (是否有效, 验证信息)
        """
        try:
            image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'}
            image_files = []
            
            for file in os.listdir(save_path):
                if any(file.lower().endswith(ext) for ext in image_extensions):
                    file_path = os.path.join(save_path, file)
                    
                    # 检查文件大小
                    if os.path.getsize(file_path) == 0:
                        return False, f"图片文件大小为0: {file}"
                    
                    # 检查文件是否可读
                    if not os.access(file_path, os.R_OK):
                        return False, f"图片文件不可读: {file}"
                    
                    # 简单的文件头验证
                    if not self._validate_image_header(file_path):
                        return False, f"图片文件头无效: {file}"
                    
                    image_files.append(file)
            
            if not image_files:
                return False, "没有找到有效的图片文件"
            
            return True, f"找到 {len(image_files)} 个有效图片文件"
        
        except Exception as e:
            return False, f"验证图片文件失败: {e}"
    
    def _validate_image_header(self, file_path: str) -> bool:
        """
        验证图片文件头
        
        Args:
            file_path: 图片文件路径
            
        Returns:
            bool: 文件头是否有效
        """
        try:
            with open(file_path, 'rb') as f:
                header = f.read(16)
            
            # 常见图片格式的文件头
            image_signatures = [
                b'\xff\xd8\xff',      # JPEG
                b'\x89PNG\r\n\x1a\n', # PNG
                b'GIF87a',            # GIF87a
                b'GIF89a',            # GIF89a
                b'RIFF',              # WEBP (RIFF container)
                b'BM',                # BMP
            ]
            
            for signature in image_signatures:
                if header.startswith(signature):
                    return True
            
            return False
        
        except Exception:
            return False
    
    def _validate_multi_page_artwork(self, artwork: Artwork, save_path: str) -> Tuple[bool, str]:
        """
        验证多页作品的完整性
        
        Args:
            artwork: 作品对象
            save_path: 保存路径
            
        Returns:
            Tuple[bool, str]: (是否有效, 验证信息)
        """
        try:
            if not artwork.page_count:
                return True, "无法验证页面数量（缺少页面信息）"
            
            expected_pages = artwork.page_count
            
            # 查找页面文件
            page_files = []
            for i in range(expected_pages):
                # 查找可能的文件名格式
                possible_names = [
                    f"p{i}.jpg", f"p{i}.jpeg", f"p{i}.png", 
                    f"p{i}.gif", f"p{i}.webp", f"p{i}.bmp"
                ]
                
                found = False
                for name in possible_names:
                    file_path = os.path.join(save_path, name)
                    if os.path.exists(file_path):
                        page_files.append(name)
                        found = True
                        break
                
                if not found:
                    return False, f"缺少页面文件: p{i}.*"
            
            if len(page_files) < expected_pages:
                return False, f"页面数量不足: 期望 {expected_pages}, 实际 {len(page_files)}"
            
            return True, f"多页作品验证通过: {len(page_files)}/{expected_pages} 页"
        
        except Exception as e:
            return False, f"验证多页作品失败: {e}"
    
    def _validate_metadata_file(self, save_path: str) -> Tuple[bool, str]:
        """
        验证元数据文件
        
        Args:
            save_path: 保存路径
            
        Returns:
            Tuple[bool, str]: (是否有效, 验证信息)
        """
        try:
            metadata_file = os.path.join(save_path, "metadata.json")
            
            if not os.path.exists(metadata_file):
                return False, "元数据文件不存在"
            
            if os.path.getsize(metadata_file) == 0:
                return False, "元数据文件为空"
            
            # 尝试解析JSON
            import json
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            # 检查必要字段
            required_fields = ['id', 'title', 'author_id', 'author_name']
            for field in required_fields:
                if field not in metadata:
                    return False, f"元数据缺少必要字段: {field}"
            
            return True, "元数据文件验证通过"
        
        except json.JSONDecodeError as e:
            return False, f"元数据文件JSON格式错误: {e}"
        except Exception as e:
            return False, f"验证元数据文件失败: {e}"
    
    def check_artwork_downloaded(self, artwork: Artwork) -> Tuple[bool, str]:
        """
        检查作品是否已下载并验证完整性
        
        Args:
            artwork: 作品对象
            
        Returns:
            Tuple[bool, str]: (是否已下载且有效, 保存路径)
        """
        try:
            # 生成预期的保存路径
            from .artwork_processor import ArtworkProcessor
            processor = ArtworkProcessor(self.download_config)
            expected_path = processor.generate_save_path(artwork)
            
            # 检查路径是否存在
            if not os.path.exists(expected_path):
                return False, expected_path
            
            # 验证下载完整性
            is_valid, message = self.validate_artwork_download(artwork, expected_path)
            
            if is_valid:
                return True, expected_path
            else:
                self.logger.warning(f"⚠️ 作品 {artwork.id} 下载不完整: {message}")
                return False, expected_path
        
        except Exception as e:
            self.logger.error(f"❌ 检查作品下载状态失败: {artwork.id} - {e}")
            return False, ""
    
    def get_validation_report(self, save_path: str) -> Dict[str, Any]:
        """
        获取详细的验证报告
        
        Args:
            save_path: 保存路径
            
        Returns:
            Dict[str, Any]: 验证报告
        """
        report = {
            'path': save_path,
            'exists': False,
            'is_directory': False,
            'file_count': 0,
            'image_files': [],
            'other_files': [],
            'total_size': 0,
            'has_metadata': False,
            'validation_errors': []
        }
        
        try:
            if os.path.exists(save_path):
                report['exists'] = True
                report['is_directory'] = os.path.isdir(save_path)
                
                if report['is_directory']:
                    files = os.listdir(save_path)
                    report['file_count'] = len(files)
                    
                    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'}
                    
                    for file in files:
                        file_path = os.path.join(save_path, file)
                        file_size = os.path.getsize(file_path)
                        report['total_size'] += file_size
                        
                        if any(file.lower().endswith(ext) for ext in image_extensions):
                            report['image_files'].append({
                                'name': file,
                                'size': file_size,
                                'valid_header': self._validate_image_header(file_path)
                            })
                        else:
                            report['other_files'].append({
                                'name': file,
                                'size': file_size
                            })
                    
                    # 检查元数据文件
                    metadata_file = os.path.join(save_path, "metadata.json")
                    report['has_metadata'] = os.path.exists(metadata_file)
            
        except Exception as e:
            report['validation_errors'].append(f"生成验证报告失败: {e}")
        
        return report
    
    def repair_download(self, artwork: Artwork, save_path: str) -> List[str]:
        """
        尝试修复下载问题
        
        Args:
            artwork: 作品对象
            save_path: 保存路径
            
        Returns:
            List[str]: 修复操作列表
        """
        repair_actions = []
        
        try:
            # 检查并创建目录
            if not os.path.exists(save_path):
                os.makedirs(save_path, exist_ok=True)
                repair_actions.append(f"创建目录: {save_path}")
            
            # 移除损坏的文件
            if os.path.exists(save_path):
                for file in os.listdir(save_path):
                    file_path = os.path.join(save_path, file)
                    
                    # 检查空文件
                    if os.path.getsize(file_path) == 0:
                        os.remove(file_path)
                        repair_actions.append(f"移除空文件: {file}")
                    
                    # 检查损坏的图片文件
                    elif file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp')):
                        if not self._validate_image_header(file_path):
                            os.remove(file_path)
                            repair_actions.append(f"移除损坏的图片文件: {file}")
            
        except Exception as e:
            repair_actions.append(f"修复过程出错: {e}")
        
        return repair_actions
