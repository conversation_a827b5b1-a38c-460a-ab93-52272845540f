"""
下载编排器

专门负责控制下载流程和协调各个组件
"""

import logging
from typing import List, Dict, Any

from .spider_context import SpiderContext
from .authentication_manager import AuthenticationManager
from .component_manager import ComponentManager
from ..models.artwork import Artwork
from ..models.exceptions import PixivSpiderError


class DownloadOrchestrator:
    """下载编排器 - 专注下载流程控制"""
    
    def __init__(self, context: SpiderContext, auth_manager: AuthenticationManager, component_manager: ComponentManager):
        """
        初始化下载编排器
        
        Args:
            context: 爬虫上下文
            auth_manager: 认证管理器
            component_manager: 组件管理器
        """
        self.context = context
        self.auth_manager = auth_manager
        self.component_manager = component_manager
        self.logger = logging.getLogger(__name__)
    
    def start_download(self) -> Dict[str, Any]:
        """
        开始下载
        
        Returns:
            Dict[str, Any]: 下载结果统计
        """
        if self.component_manager.resource_manager._is_running:
            raise PixivSpiderError("爬虫已在运行中")
        
        # 重置资源状态
        self._reset_for_new_download()
        
        # 检查认证状态
        self._ensure_authentication()
        
        try:
            # 启动监控和设置状态
            self._setup_download_environment()
            
            # 验证配置和环境
            self._validate_download_environment()
            
            # 获取作品列表
            artworks = self._get_artworks()
            
            if not artworks:
                self.component_manager.resource_manager._notify_status("没有找到符合条件的作品")
                return {'total': 0, 'success': 0, 'failed': 0, 'skipped': 0}
            
            self.logger.info(f"找到 {len(artworks)} 个作品，开始下载")
            self.component_manager.resource_manager._notify_status(f"找到 {len(artworks)} 个作品，开始下载...")
            
            # 执行下载
            stats = self._execute_download(artworks)
            
            self.component_manager.resource_manager._notify_status("下载任务完成")
            return stats
            
        except Exception as e:
            self.logger.error(f"下载过程中发生错误: {e}")
            self.component_manager.resource_manager._notify_status(f"下载失败: {e}")
            raise
        finally:
            self.component_manager.resource_manager.set_running_state(False)
    
    def _reset_for_new_download(self):
        """重置下载状态"""
        self.component_manager.resource_manager.reset_for_new_download(
            self.context.service_provider,
            self.auth_manager.cookies
        )
        
        # 检查停止标志
        if (hasattr(self.component_manager.resource_manager, 'download_service') and 
            self.component_manager.resource_manager.download_service and
            hasattr(self.component_manager.resource_manager.download_service, '_should_stop') and
            self.component_manager.resource_manager.download_service._should_stop):
            
            self.logger.warning("🛑 重置后仍检测到停止信号，强制重置")
            self.component_manager.resource_manager.download_service._should_stop = False
            self.component_manager.resource_manager.download_service._is_stopping = False
            self.logger.info("🔧 已强制重置停止标志")
    
    def _ensure_authentication(self):
        """确保认证状态"""
        if not self.auth_manager.is_authenticated:
            self.logger.info("未认证，尝试自动登录...")
            auth_result = self.auth_manager.authenticate()
            self.logger.info(f"自动认证结果: {auth_result}")
            
            if not auth_result:
                self.logger.warning("自动认证失败，将尝试在无认证状态下继续")
                self.logger.warning("如果遇到登录页面，请在浏览器中手动登录")
                self.component_manager.resource_manager._notify_status("⚠️ 未找到登录信息，如需登录请在浏览器中操作")
    
    def _setup_download_environment(self):
        """设置下载环境"""
        # 重置状态
        self.component_manager.resource_manager.reset_for_new_download(
            self.context.service_provider,
            self.auth_manager.cookies
        )
        
        # 启动内存监控
        self.component_manager.start_monitoring()
        
        # 设置运行状态
        self.component_manager.resource_manager.set_running_state(True)
        self.component_manager.resource_manager._notify_status("开始下载...")
    
    def _validate_download_environment(self):
        """验证下载环境"""
        # 验证配置
        errors = self.context.download_config.validate()
        if errors:
            raise PixivSpiderError(f"配置验证失败: {', '.join(errors)}")
        
        # 确保目录存在
        self.context.config_manager.ensure_directories()
        
        # 确保Selenium驱动器已初始化
        if not self.component_manager.ensure_selenium_driver():
            raise PixivSpiderError("Selenium驱动器初始化失败")
        
        # 初始化处理器
        self.component_manager.init_processors()
        
        # 设置服务集成
        self.component_manager.setup_services_integration()
    
    def _get_artworks(self) -> List[Artwork]:
        """获取作品列表"""
        # 检查停止信号
        if self._should_stop():
            self.logger.warning("🛑 获取作品前检测到停止信号，终止下载")
            return []
        
        # 根据下载模式获取作品
        artworks = self.component_manager.download_mode_handler.get_artworks_by_mode()
        
        # 再次检查停止信号
        if self._should_stop():
            self.logger.warning("🛑 获取作品后检测到停止信号，终止下载")
            return []
        
        return artworks
    
    def _execute_download(self, artworks: List[Artwork]) -> Dict[str, Any]:
        """执行下载"""
        # 最后检查停止信号
        if self._should_stop():
            self.logger.warning("🛑 开始下载前检测到停止信号，终止下载")
            return {'total': len(artworks), 'success': 0, 'failed': 0, 'skipped': len(artworks)}
        
        # 使用路径管理器执行下载
        return self._execute_download_with_path(artworks)
    
    def _execute_download_with_path(self, artworks: List[Artwork]) -> Dict[str, Any]:
        """使用正确的路径执行下载"""
        download_path = self.component_manager.path_manager.get_download_path_for_mode()
        validated_path = self.component_manager.path_manager.ensure_download_directory(download_path)
        
        # 临时修改路径配置
        original_save_path = self.context.download_config.save_path
        try:
            self.component_manager.path_manager.update_download_service_path(
                self.context.download_service, validated_path, original_save_path
            )
            
            self.logger.info(f"🚀 开始批量下载 {len(artworks)} 个作品...")
            
            try:
                # 最后检查停止信号
                if self._should_stop():
                    self.logger.warning("🛑 调用batch_download前检测到停止信号，终止下载")
                    return {'total': len(artworks), 'success': 0, 'failed': 0, 'skipped': len(artworks)}
                
                self.logger.info("🔧 正在调用 batch_download 方法...")
                result = self.context.download_service.batch_download(artworks)
                self.logger.info(f"🔧 batch_download 方法返回: {result}")
                
                # 下载完成后清理Selenium资源
                self.logger.info("🧹 下载完成，开始清理Selenium资源...")
                self.component_manager.resource_manager.cleanup_selenium_after_download()
                
                return result
                
            except Exception as e:
                self.logger.error(f"❌ batch_download 方法异常: {e}")
                import traceback
                self.logger.error(f"❌ 异常堆栈: {traceback.format_exc()}")
                
                # 异常情况下也要清理Selenium资源
                self.logger.info("🧹 异常情况下清理Selenium资源...")
                self.component_manager.resource_manager.cleanup_selenium_after_download()
                
                raise
                
        finally:
            # 恢复原始路径
            self.component_manager.path_manager.restore_download_service_path(
                self.context.download_service, original_save_path
            )
    
    def _should_stop(self) -> bool:
        """检查是否应该停止"""
        return (hasattr(self.context.download_service, '_should_stop') and 
                self.context.download_service._should_stop)
    
    def stop_download(self) -> None:
        """停止下载"""
        self.component_manager.resource_manager.stop_all_services()
    
    def pause_download(self) -> None:
        """暂停下载"""
        self.component_manager.resource_manager.pause_all_services()
    
    def resume_download(self) -> bool:
        """继续下载"""
        return self.component_manager.resource_manager.resume_all_services()
