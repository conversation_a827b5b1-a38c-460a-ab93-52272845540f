"""
重构后的Pixiv API服务

整合各个API组件，提供统一的API服务接口
"""

import logging
from typing import Optional, Dict, Any, List

from ....interfaces.api_interface import IApiService
from ....config.config_manager import ConfigManager
from ....infrastructure.network.session_manager import SessionManager
from ....infrastructure.network.http_client import HttpClient
from .api_cache_manager import ApiCacheManager
from .pixiv_api_endpoints import PixivApiEndpoints


class PixivApiService(IApiService):
    """重构后的Pixiv API服务 - 整合各个组件"""
    
    def __init__(self, cookies: List[Dict[str, Any]], config_manager: Optional[ConfigManager] = None):
        """
        初始化API服务
        
        Args:
            cookies: 认证cookies
            config_manager: 配置管理器
        """
        self.config_manager = config_manager or ConfigManager()
        self.spider_config = self.config_manager.load_spider_config()
        self.logger = logging.getLogger(__name__)
        
        # 初始化各个组件
        self.session_manager = SessionManager(cookies, self.spider_config)
        self.http_client = HttpClient(self.session_manager, self.spider_config)
        self.cache_manager = ApiCacheManager(self.spider_config)
        self.api_endpoints = PixivApiEndpoints(self.http_client, self.cache_manager)
        
        self.logger.info("🚀 Pixiv API服务初始化完成")
    
    # ===== IApiService接口实现 =====
    
    def get_artwork_detail(self, artwork_id: int) -> Optional[Dict[str, Any]]:
        """
        获取作品详情
        
        Args:
            artwork_id: 作品ID
            
        Returns:
            Optional[Dict[str, Any]]: 作品详情数据
        """
        return self.api_endpoints.get_artwork_detail(artwork_id)
    
    def get_artwork_pages(self, artwork_id: int, stop_signal_callback=None) -> Optional[Dict[str, Any]]:
        """
        获取作品页面信息
        
        Args:
            artwork_id: 作品ID
            stop_signal_callback: 停止信号回调函数
            
        Returns:
            Optional[Dict[str, Any]]: 页面信息数据
        """
        return self.api_endpoints.get_artwork_pages(artwork_id, stop_signal_callback)
    
    def get_artwork_details_batch(self, artwork_ids: List[int], max_workers: int = None) -> Dict[int, Optional[Dict[str, Any]]]:
        """
        批量获取作品详情
        
        Args:
            artwork_ids: 作品ID列表
            max_workers: 最大并发数
            
        Returns:
            Dict[int, Optional[Dict[str, Any]]]: 作品ID到详情数据的映射
        """
        return self.api_endpoints.get_artwork_details_batch(artwork_ids, max_workers)
    
    def get_user_artworks(self, user_id: int, page: int = 1) -> Optional[Dict[str, Any]]:
        """
        获取用户作品列表
        
        Args:
            user_id: 用户ID
            page: 页码
            
        Returns:
            Optional[Dict[str, Any]]: 用户作品数据
        """
        return self.api_endpoints.get_user_artworks(user_id, page)
    
    def make_request(self, url: str, method: str = 'GET', **kwargs) -> Optional[Any]:
        """
        通用请求方法
        
        Args:
            url: 请求URL
            method: 请求方法
            **kwargs: 其他请求参数
            
        Returns:
            Optional[Any]: 响应对象
        """
        return self.http_client.make_request(url, method, **kwargs)
    
    # ===== 扩展API方法 =====
    
    def get_ranking_artworks(self, mode: str = "daily", date: str = None, page: int = 1) -> Optional[Dict[str, Any]]:
        """
        获取排行榜作品
        
        Args:
            mode: 排行榜模式
            date: 日期
            page: 页码
            
        Returns:
            Optional[Dict[str, Any]]: 排行榜数据
        """
        return self.api_endpoints.get_ranking_artworks(mode, date, page)
    
    def search_artworks(self, keyword: str, order: str = "date_desc", 
                       mode: str = "all", page: int = 1) -> Optional[Dict[str, Any]]:
        """
        搜索作品
        
        Args:
            keyword: 搜索关键词
            order: 排序方式
            mode: 搜索模式
            page: 页码
            
        Returns:
            Optional[Dict[str, Any]]: 搜索结果数据
        """
        return self.api_endpoints.search_artworks(keyword, order, mode, page)
    
    def download_file(self, url: str, save_path: str, stop_signal_callback=None) -> bool:
        """
        下载文件
        
        Args:
            url: 文件URL
            save_path: 保存路径
            stop_signal_callback: 停止信号回调函数
            
        Returns:
            bool: 是否下载成功
        """
        return self.http_client.download_file(url, save_path, stop_signal_callback)
    
    def get_user_profile(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        获取用户资料
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[Dict[str, Any]]: 用户资料数据
        """
        return self.api_endpoints.get_user_profile(user_id)
    
    def get_artwork_ugoira_meta(self, artwork_id: int) -> Optional[Dict[str, Any]]:
        """
        获取动图元数据
        
        Args:
            artwork_id: 作品ID
            
        Returns:
            Optional[Dict[str, Any]]: 动图元数据
        """
        return self.api_endpoints.get_artwork_ugoira_meta(artwork_id)
    
    def get_related_artworks(self, artwork_id: int, limit: int = 18) -> Optional[Dict[str, Any]]:
        """
        获取相关作品
        
        Args:
            artwork_id: 作品ID
            limit: 限制数量
            
        Returns:
            Optional[Dict[str, Any]]: 相关作品数据
        """
        return self.api_endpoints.get_related_artworks(artwork_id, limit)
    
    # ===== 管理和维护方法 =====
    
    def update_cookies(self, new_cookies: List[Dict[str, Any]]) -> None:
        """
        更新cookies
        
        Args:
            new_cookies: 新的cookies列表
        """
        self.session_manager.update_cookies(new_cookies)
        self.logger.info("🍪 API服务cookies已更新")
    
    def clear_cache(self) -> None:
        """清空API缓存"""
        self.cache_manager.clear_cache()
        self.logger.info("🗑️ API缓存已清空")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存信息
        
        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        return self.cache_manager.get_cache_info()
    
    def get_service_status(self) -> Dict[str, Any]:
        """
        获取服务状态
        
        Returns:
            Dict[str, Any]: 服务状态信息
        """
        return {
            'session_pool': self.session_manager.get_pool_status(),
            'cache_info': self.cache_manager.get_cache_info(),
            'http_client': self.http_client.get_client_info(),
            'api_connectivity': self.test_connectivity()
        }
    
    def test_connectivity(self) -> bool:
        """
        测试API连接性
        
        Returns:
            bool: 连接是否正常
        """
        return self.api_endpoints.test_api_connectivity()
    
    def optimize_performance(self) -> None:
        """优化性能"""
        # 优化缓存
        self.cache_manager.optimize_cache()
        
        # 测试会话池
        if not self.session_manager.test_session():
            self.logger.warning("⚠️ 会话测试失败，重新创建会话池")
            self.session_manager.recreate_pool()
    
    def cleanup_sessions(self) -> None:
        """清理所有会话"""
        try:
            self.logger.info("🧹 开始清理API服务资源...")
            
            # 清理会话管理器
            self.session_manager.cleanup_sessions()
            
            # 清理缓存
            self.cache_manager.clear_cache()
            
            self.logger.info("✅ API服务资源清理完成")
        
        except Exception as e:
            self.logger.error(f"❌ 清理API服务资源失败: {e}")
    
    def __del__(self):
        """析构函数，清理资源"""
        try:
            self.cleanup_sessions()
        except Exception:
            # 析构函数中不应该抛出异常
            pass
