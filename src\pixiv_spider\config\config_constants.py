"""
配置常量定义

统一管理所有配置相关的常量和默认值
"""

from pathlib import Path
from typing import Dict, Any

# ===== 应用信息 =====
APP_NAME = "Pixiv Spider"
APP_VERSION = "6.0.0"
APP_AUTHOR = "Pixiv Spider Team"

# ===== 文件和目录路径 =====
class Paths:
    """路径常量"""
    # 配置文件
    CONFIG_FILE = "pixiv_config.json"
    CACHE_FILE = "pixiv_cache.pkl"
    LOG_FILE = "pixiv_spider.log"
    
    # 目录
    DOWNLOAD_DIR = "pixiv_imgs"
    TEMP_DIR = "temp"
    LOG_DIR = "logs"
    COOKIES_DIR = "cookies"
    
    # Cookie文件
    COOKIES_FILE = "pixiv_cookies.pkl"
    
    @classmethod
    def get_cookies_path(cls) -> str:
        """获取Cookie文件完整路径"""
        return f"{cls.COOKIES_DIR}/{cls.COOKIES_FILE}"

# ===== API相关常量 =====
class ApiConstants:
    """API相关常量"""
    PIXIV_BASE_URL = "https://www.pixiv.net"
    PIXIV_API_BASE_URL = "https://app-api.pixiv.net"
    PIXIV_AJAX_URL = "https://www.pixiv.net/ajax"
    
    # 请求头
    DEFAULT_USER_AGENT = (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
        "(KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    )
    DEFAULT_REFERER = "https://www.pixiv.net/"

# ===== 网络设置默认值 =====
class NetworkDefaults:
    """网络设置默认值"""
    TIMEOUT = 30
    RETRY_ATTEMPTS = 3
    RETRY_DELAY = 1.0
    REQUEST_INTERVAL = 0.5

# ===== 性能设置默认值 =====
class PerformanceDefaults:
    """性能设置默认值"""
    MAX_WORKERS = 8
    CONCURRENT_DOWNLOADS = 4
    API_CACHE_SIZE = 1000
    CACHE_EXPIRE_TIME = 3600

# ===== Selenium设置默认值 =====
class SeleniumDefaults:
    """Selenium设置默认值"""
    HEADLESS = True
    TIMEOUT = 10
    PAGE_LOAD_TIMEOUT = 30

# ===== 日志设置默认值 =====
class LogDefaults:
    """日志设置默认值"""
    LEVEL = "INFO"
    MAX_SIZE = 10 * 1024 * 1024  # 10MB
    BACKUP_COUNT = 5

# ===== GUI设置默认值 =====
class GuiDefaults:
    """GUI设置默认值"""
    WINDOW_WIDTH = 800
    WINDOW_HEIGHT = 600
    MIN_WIDTH = 600
    MIN_HEIGHT = 400

# ===== 下载设置默认值 =====
class DownloadDefaults:
    """下载设置默认值"""
    START_PAGE = 1
    END_PAGE = 5
    DAYS = 1
    MIN_BOOKMARKS = 0
    MAX_BOOKMARKS = 0
    DOWNLOAD_LIMIT = 0

# ===== 文件类型常量 =====
class FileTypes:
    """支持的文件类型"""
    IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
    ANIMATION_EXTENSIONS = ['.gif', '.webm', '.mp4']
    
    # 文件大小限制
    MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
    MAX_FILENAME_LENGTH = 255

# ===== 配置模板 =====
class ConfigTemplates:
    """配置模板"""
    
    @staticmethod
    def get_default_download_config() -> Dict[str, Any]:
        """获取默认下载配置"""
        return {
            'save_path': Paths.DOWNLOAD_DIR,
            'search_save_path': '',
            'user_save_path': '',
            'download_mode': 'date',
            'gif_mode': 'gif_only',
            'classify_mode': 'by_date',
            'start_page': DownloadDefaults.START_PAGE,
            'end_page': DownloadDefaults.END_PAGE,
            'days': DownloadDefaults.DAYS,
            'date_mode': 'by_date_range',
            'search_keyword': '',
            'search_order': 'date_desc',
            'search_mode': 'all',
            'search_config': {},
            'search_sub_mode': 'by_page_range',
            'user_id': None,
            'min_bookmarks': DownloadDefaults.MIN_BOOKMARKS,
            'max_bookmarks': DownloadDefaults.MAX_BOOKMARKS,
            'min_pages': 0,
            'max_pages': 0,
            'exclude_tags': [],
            'include_tags': [],
            'skip_existing': True,
            'create_info_file': True,
            'download_limit': DownloadDefaults.DOWNLOAD_LIMIT,
            'ranking_config': {}
        }
    
    @staticmethod
    def get_default_spider_config() -> Dict[str, Any]:
        """获取默认爬虫配置"""
        return {
            'max_workers': PerformanceDefaults.MAX_WORKERS,
            'request_timeout': NetworkDefaults.TIMEOUT,
            'retry_attempts': NetworkDefaults.RETRY_ATTEMPTS,
            'retry_delay': NetworkDefaults.RETRY_DELAY,
            'concurrent_downloads': PerformanceDefaults.CONCURRENT_DOWNLOADS,
            'api_cache_size': PerformanceDefaults.API_CACHE_SIZE,
            'cache_expire_time': PerformanceDefaults.CACHE_EXPIRE_TIME,
            'user_agent': ApiConstants.DEFAULT_USER_AGENT,
            'referer': ApiConstants.DEFAULT_REFERER,
            'proxy': None,
            'selenium_headless': SeleniumDefaults.HEADLESS,
            'selenium_timeout': SeleniumDefaults.TIMEOUT,
            'selenium_page_load_timeout': SeleniumDefaults.PAGE_LOAD_TIMEOUT,
            'log_level': LogDefaults.LEVEL,
            'log_file': None,
            'log_max_size': LogDefaults.MAX_SIZE,
            'log_backup_count': LogDefaults.BACKUP_COUNT,
            'cookies_file': Paths.get_cookies_path(),
            'config_file': Paths.CONFIG_FILE
        }

# ===== 配置验证规则 =====
class ValidationRules:
    """配置验证规则"""
    
    # 数值范围
    MIN_PAGE = 1
    MAX_PAGE = 1000
    MIN_DAYS = 1
    MAX_DAYS = 365
    MIN_WORKERS = 1
    MAX_WORKERS = 32
    MIN_TIMEOUT = 5
    MAX_TIMEOUT = 300
    
    # 字符串长度
    MAX_PATH_LENGTH = 260
    MAX_KEYWORD_LENGTH = 100
    MAX_TAG_LENGTH = 50
    
    @staticmethod
    def get_validation_rules() -> Dict[str, Dict[str, Any]]:
        """获取验证规则字典"""
        return {
            'page_range': {
                'min': ValidationRules.MIN_PAGE,
                'max': ValidationRules.MAX_PAGE
            },
            'days_range': {
                'min': ValidationRules.MIN_DAYS,
                'max': ValidationRules.MAX_DAYS
            },
            'workers_range': {
                'min': ValidationRules.MIN_WORKERS,
                'max': ValidationRules.MAX_WORKERS
            },
            'timeout_range': {
                'min': ValidationRules.MIN_TIMEOUT,
                'max': ValidationRules.MAX_TIMEOUT
            },
            'path_length': {
                'max': ValidationRules.MAX_PATH_LENGTH
            },
            'keyword_length': {
                'max': ValidationRules.MAX_KEYWORD_LENGTH
            },
            'tag_length': {
                'max': ValidationRules.MAX_TAG_LENGTH
            }
        }

# ===== 环境配置 =====
class Environment:
    """环境相关配置"""
    
    @staticmethod
    def is_development() -> bool:
        """是否为开发环境"""
        import os
        return os.getenv('PIXIV_SPIDER_ENV', 'production').lower() == 'development'
    
    @staticmethod
    def get_config_dir() -> Path:
        """获取配置目录"""
        import os
        if Environment.is_development():
            return Path.cwd() / 'dev_config'
        return Path.cwd()
    
    @staticmethod
    def get_log_level() -> str:
        """获取日志级别"""
        import os
        if Environment.is_development():
            return os.getenv('PIXIV_SPIDER_LOG_LEVEL', 'DEBUG')
        return os.getenv('PIXIV_SPIDER_LOG_LEVEL', LogDefaults.LEVEL)

# ===== 配置兼容性 =====
class Compatibility:
    """配置兼容性处理"""
    
    # 配置版本映射
    VERSION_MAPPING = {
        '1.0.0': 'legacy',
        '2.0.0': 'legacy',
        '3.0.0': 'current',
        '6.0.0': 'current'
    }
    
    # 已废弃的配置项
    DEPRECATED_KEYS = [
        'old_save_path',
        'legacy_mode',
        'deprecated_setting'
    ]
    
    # 配置项重命名映射
    KEY_MAPPING = {
        'old_key': 'new_key',
        'legacy_setting': 'modern_setting'
    }
    
    @staticmethod
    def migrate_config(config_data: Dict[str, Any], from_version: str) -> Dict[str, Any]:
        """迁移配置数据"""
        # 移除废弃的配置项
        for key in Compatibility.DEPRECATED_KEYS:
            config_data.pop(key, None)
        
        # 重命名配置项
        for old_key, new_key in Compatibility.KEY_MAPPING.items():
            if old_key in config_data:
                config_data[new_key] = config_data.pop(old_key)
        
        return config_data
