<template>
  <el-card class="download-settings-card modern-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="header-left">
          <el-icon class="header-icon"><Download /></el-icon>
          <span class="header-title">下载设置</span>
        </div>
      </div>
    </template>

    <el-form :model="currentSettings" label-width="80px" size="small" class="modern-form">
      <!-- 下载模式 -->
      <el-form-item label="下载模式">
        <el-select
          v-model="downloadMode"
          @change="onModeChange"
          style="width: 100%"
        >
          <el-option label="关注画师作品" value="following" />
          <el-option label="搜索作品" value="search" />
          <el-option label="排行榜作品" value="ranking" />
          <el-option label="画师作品" value="artist" />
        </el-select>
      </el-form-item>

      <!-- 文件分类模式 -->
      <el-form-item label="文件分类">
        <el-select
          v-model="classifyMode"
          @change="saveSettings"
          style="width: 100%"
        >
          <el-option label="按日期分类" value="by_date" />
          <el-option label="按作者分类" value="by_author" />
          <el-option label="按类型分类" value="by_type" />
          <el-option label="平铺结构" value="flat" />
        </el-select>
      </el-form-item>

      <!-- 关注画师作品设置 -->
      <template v-if="downloadMode === 'following'">
        <el-form-item label="下载方式">
          <el-radio-group v-model="followingSettings.downloadType" @change="saveSettings">
            <el-radio label="pages">按页码</el-radio>
            <el-radio label="days">按天数</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="followingSettings.downloadType === 'days'" label="获取天数">
          <el-input-number
            v-model="followingSettings.days"
            :min="1"
            :max="365"
            style="width: 100%"
            @change="saveSettings"
          />
        </el-form-item>

        <el-form-item v-if="followingSettings.downloadType === 'pages'" label="页码范围">
          <div class="range-input">
            <el-input-number
              v-model="followingSettings.pageStart"
              :min="1"
              :max="followingSettings.pageEnd"
              size="small"
            />
            <span class="range-separator">至</span>
            <el-input-number
              v-model="followingSettings.pageEnd"
              :min="followingSettings.pageStart"
              :max="1000"
              size="small"
            />
          </div>
        </el-form-item>

        <el-form-item label="下载路径">
          <div class="path-input">
            <el-input
              v-model="followingSettings.downloadPath"
              placeholder="H:\Pixiv"
              readonly
            />
            <el-button @click="selectPath('following')" size="small">
              选择路径
            </el-button>
          </div>
        </el-form-item>
      </template>

      <!-- 搜索作品设置 -->
      <template v-if="downloadMode === 'search'">
        <el-form-item label="搜索关键词">
          <el-input
            v-model="searchSettings.keyword"
            placeholder="请输入搜索关键词"
            clearable
            @change="saveSettings"
          />
        </el-form-item>

        <el-form-item label="搜索范围">
          <el-select v-model="searchSettings.searchType" @change="saveSettings" style="width: 100%">
            <el-option label="插画·漫画" value="artworks" />
            <el-option label="插画" value="illustrations" />
            <el-option label="漫画" value="manga" />
          </el-select>
        </el-form-item>

        <el-form-item label="收藏过滤">
          <div class="bookmark-filter">
            <el-switch
              v-model="searchSettings.enableBookmarkFilter"
              active-text="启用收藏过滤"
              inactive-text="不启用"
              @change="saveSettings"
            />
            <el-select
              v-if="searchSettings.enableBookmarkFilter"
              v-model="searchSettings.bookmarkCount"
              @change="saveSettings"
              style="width: 150px; margin-left: 10px"
            >
              <el-option label="1000users入り" :value="1000" />
              <el-option label="2000users入り" :value="2000" />
              <el-option label="5000users入り" :value="5000" />
              <el-option label="10000users入り" :value="10000" />
              <el-option label="20000users入り" :value="20000" />
            </el-select>
          </div>
        </el-form-item>

        <el-form-item label="搜索模式">
          <el-select v-model="searchSettings.searchMode" @change="saveSettings" style="width: 100%">
            <el-option label="全部" value="all" />
            <el-option label="全年龄" value="safe" />
            <el-option label="R-18" value="r18" />
          </el-select>
        </el-form-item>

        <el-form-item label="页码范围">
          <div class="range-input">
            <el-input-number
              v-model="searchSettings.pageStart"
              :min="1"
              :max="searchSettings.pageEnd"
              size="small"
            />
            <span class="range-separator">至</span>
            <el-input-number
              v-model="searchSettings.pageEnd"
              :min="searchSettings.pageStart"
              :max="1000"
              size="small"
            />
          </div>
        </el-form-item>

        <el-form-item label="下载路径">
          <div class="path-input">
            <el-input
              v-model="searchSettings.downloadPath"
              placeholder="H:\Pixiv"
              readonly
            />
            <el-button @click="selectPath('search')" size="small">
              选择路径
            </el-button>
          </div>
        </el-form-item>
      </template>

      <!-- 排行榜作品设置 -->
      <template v-if="downloadMode === 'ranking'">
        <el-form-item label="排行榜类型">
          <el-select v-model="rankingSettings.rankingType" @change="onRankingTypeChange" style="width: 100%">
            <el-option label="综合" value="overall" />
            <el-option label="插画" value="illust" />
            <el-option label="动图" value="ugoira" />
            <el-option label="漫画" value="manga" />
          </el-select>
        </el-form-item>

        <el-form-item label="排行榜选择">
          <el-select v-model="rankingSettings.rankingPeriod" @change="saveSettings" style="width: 100%">
            <el-option label="今日" value="daily" />
            <el-option label="本周" value="weekly" />
            <el-option
              v-if="rankingSettings.rankingType === 'overall'"
              label="AI生成"
              value="daily_ai"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="排行榜模式">
          <el-select v-model="rankingSettings.rankingMode" @change="saveSettings" style="width: 100%">
            <el-option label="全年龄" value="safe" />
            <el-option label="R-18" value="r18" />
          </el-select>
        </el-form-item>

        <el-form-item label="排行榜日期">
          <el-date-picker
            v-model="rankingSettings.rankingDate"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
            @change="saveSettings"
          />
        </el-form-item>

        <el-form-item label="下载路径">
          <div class="path-input">
            <el-input
              v-model="rankingSettings.downloadPath"
              placeholder="H:\Pixiv"
              readonly
            />
            <el-button @click="selectPath('ranking')" size="small">
              选择路径
            </el-button>
          </div>
        </el-form-item>
      </template>

      <!-- 画师作品设置 -->
      <template v-if="downloadMode === 'artist'">
        <el-form-item label="画师ID">
          <el-input
            v-model="artistSettings.artistId"
            placeholder="请输入画师ID"
            clearable
            @change="saveSettings"
          />
        </el-form-item>

        <el-form-item label="页码范围">
          <div class="range-input">
            <el-input-number
              v-model="artistSettings.pageStart"
              :min="1"
              :max="artistSettings.pageEnd"
              size="small"
            />
            <span class="range-separator">至</span>
            <el-input-number
              v-model="artistSettings.pageEnd"
              :min="artistSettings.pageStart"
              :max="1000"
              size="small"
            />
          </div>
        </el-form-item>

        <el-form-item label="下载路径">
          <div class="path-input">
            <el-input
              v-model="artistSettings.downloadPath"
              placeholder="H:\Pixiv"
              readonly
            />
            <el-button @click="selectPath('artist')" size="small">
              选择路径
            </el-button>
          </div>
        </el-form-item>
      </template>

      <!-- 操作按钮 -->
      <el-divider content-position="center">
        <el-text size="small">操作</el-text>
      </el-divider>

      <div class="action-buttons">
        <el-button
          type="primary"
          size="large"
          @click="startDownload"
          :disabled="!canStart"
          :loading="isStarting"
          class="control-btn start-btn"
        >
          <el-icon><VideoPlay /></el-icon>
          <span>开始下载</span>
        </el-button>

        <el-button
          type="danger"
          size="large"
          @click="stopDownload"
          :disabled="!isDownloading"
          :loading="isStopping"
          class="control-btn stop-btn"
        >
          <el-icon><VideoPause /></el-icon>
          <span>停止下载</span>
        </el-button>

        <el-button
          type="warning"
          size="large"
          @click="pauseDownload"
          :disabled="!isDownloading || isPaused"
          class="control-btn pause-btn"
        >
          <el-icon>
            <VideoPause v-if="!isPaused" />
            <VideoPlay v-else />
          </el-icon>
          <span>{{ isPaused ? '继续' : '暂停' }}</span>
        </el-button>

        <el-button @click="previewUrl" type="info" size="large">
          <el-icon><View /></el-icon>
          <span>预览URL</span>
        </el-button>
      </div>
    </el-form>
  </el-card>
</template>

<script>
import { Download, VideoPlay, VideoPause, View } from '@element-plus/icons-vue'
import { mapState } from 'vuex'
import unifiedConfigService from '../services/unified-config.js'

export default {
  name: 'DownloadSettings',
  inject: ['$api', '$ipc'],
  components: {
    Download,
    VideoPlay,
    VideoPause,
    View
  },
  data() {
    return {
      downloadMode: 'following',
      classifyMode: 'by_date', // 文件分类模式

      // 下载控制状态
      isStarting: false,
      isStopping: false,
      isPaused: false,

      // 关注画师作品设置
      followingSettings: {
        downloadType: 'pages',
        days: 7,
        pageStart: 1,
        pageEnd: 5,
        downloadPath: ''
      },

      // 搜索作品设置
      searchSettings: {
        keyword: '',
        searchType: 'artworks',
        enableBookmarkFilter: false,
        bookmarkCount: 1000,
        searchMode: 'all',
        pageStart: 1,
        pageEnd: 5,
        downloadPath: ''
      },

      // 排行榜作品设置
      rankingSettings: {
        rankingType: 'overall',
        rankingPeriod: 'daily',
        rankingMode: 'safe',
        rankingDate: (() => {
          const today = new Date()
          return today.getFullYear() + '-' +
                 String(today.getMonth() + 1).padStart(2, '0') + '-' +
                 String(today.getDate()).padStart(2, '0')
        })(),
        downloadPath: ''
      },

      // 画师作品设置
      artistSettings: {
        artistId: '',
        pageStart: 1,
        pageEnd: 5,
        downloadPath: ''
      }
    }
  },

  computed: {
    ...mapState(['isDownloading']),

    currentSettings() {
      switch (this.downloadMode) {
        case 'following':
          return this.followingSettings
        case 'search':
          return this.searchSettings
        case 'ranking':
          return this.rankingSettings
        case 'artist':
          return this.artistSettings
        default:
          return {}
      }
    },

    canStartDownload() {
      const downloadPath = this.getCurrentDownloadPath()
      if (!downloadPath) return false

      switch (this.downloadMode) {
        case 'following':
          return true
        case 'search':
          return this.searchSettings.keyword
        case 'ranking':
          return true
        case 'artist':
          return this.artistSettings.artistId
        default:
          return false
      }
    },

    canStart() {
      return this.canStartDownload && !this.isStarting && !this.isDownloading
    }
  },

  methods: {
    onModeChange() {
      // 模式改变时保存设置
      this.saveSettings()
    },

    onRankingTypeChange() {
      // 排行榜类型改变时，重置AI生成选项
      if (this.rankingSettings.rankingType !== 'overall' && this.rankingSettings.rankingPeriod === 'daily_ai') {
        this.rankingSettings.rankingPeriod = 'daily'
      }
      this.saveSettings()
    },

    async selectPath(mode) {
      try {
        const result = await window.electronAPI.showOpenDialog({
          properties: ['openDirectory'],
          title: '选择下载路径'
        })

        if (!result.canceled && result.filePaths.length > 0) {
          const path = result.filePaths[0]
          switch (mode) {
            case 'following':
              this.followingSettings.downloadPath = path
              break
            case 'search':
              this.searchSettings.downloadPath = path
              break
            case 'ranking':
              this.rankingSettings.downloadPath = path
              break
            case 'artist':
              this.artistSettings.downloadPath = path
              break
          }
          this.saveSettings()
        }
      } catch (error) {
        this.$message.error('选择路径失败')
      }
    },

    saveSettings() {
      try {
        // 保存当前设置到store，确保所有数据都可序列化
        const settings = {
          mode: this.downloadMode,
          classifyMode: this.classifyMode, // 添加文件分类模式
          following: JSON.parse(JSON.stringify(this.followingSettings)),
          search: JSON.parse(JSON.stringify(this.searchSettings)),
          ranking: JSON.parse(JSON.stringify(this.rankingSettings)),
          artist: JSON.parse(JSON.stringify(this.artistSettings))
        }

        console.log('💾 保存下载设置:', settings)
        this.$store.commit('updateDownloadSettings', settings)

        // 持久化保存设置
        this.$store.dispatch('saveSettings').then(() => {
          console.log('✅ 设置已保存到存储')
        }).catch(error => {
          console.error('❌ 保存设置失败:', error)
        })
      } catch (error) {
        console.error('❌ 准备保存设置时出错:', error)
      }
    },

    // 移除重复的URL生成方法，使用统一配置服务
    generateUrl() {
      const currentSettings = this.getCurrentSettings()
      return unifiedConfigService.generatePreviewUrl(this.downloadMode, currentSettings)
    },

    previewUrl() {
      const currentSettings = this.getCurrentSettings()
      const url = unifiedConfigService.generatePreviewUrl(this.downloadMode, currentSettings)

      if (url) {
        this.$message.info(`生成的URL: ${url}`)
        console.log('Generated URL:', url)
      } else {
        this.$message.warning('请先完成设置')
      }
    },

    getCurrentDownloadPath() {
      const mode = this.downloadMode || 'following'

      switch (mode) {
        case 'following':
          return this.followingSettings?.downloadPath
        case 'search':
          return this.searchSettings?.downloadPath
        case 'ranking':
          return this.rankingSettings?.downloadPath
        case 'artist':
          return this.artistSettings?.downloadPath
        default:
          return null
      }
    },

    // 使用统一配置服务替换重复的配置转换逻辑
    convertToBackendConfig(mode, settings) {
      return unifiedConfigService.createUnifiedConfig(mode, settings)
    },

    async startDownload() {
      if (!this.canStartDownload) {
        this.$message.warning('请完成必要的设置')
        return
      }

      this.isStarting = true

      try {
        const mode = this.downloadMode || 'following'
        const currentSettings = this.getCurrentSettings()

        // 使用统一配置服务创建后端配置
        const downloadConfig = this.convertToBackendConfig(mode, currentSettings)

        console.log('🚀 准备开始下载，配置:', downloadConfig)

        // 验证配置是否可序列化
        try {
          JSON.stringify(downloadConfig)
          console.log('✅ 配置序列化验证通过')
        } catch (e) {
          console.error('❌ 配置序列化失败:', e)
          throw new Error('配置包含不可序列化的对象: ' + e.message)
        }

        // 调用后端API开始下载
        console.log('📡 调用API开始下载...')
        const response = await this.$api.startDownload(downloadConfig)

        if (response.success) {
          this.$store.commit('setDownloading', true)
          this.$store.commit('addLog', '🚀 下载任务已提交到后台')
          this.$message.success('下载任务已在后台启动，请查看日志获取进度')
        } else {
          throw new Error(response.message || '启动下载失败')
        }
      } catch (error) {
        console.error('开始下载失败:', error)
        this.$store.commit('addLog', `❌ 下载启动失败: ${error.message}`)
        this.$message.error('开始下载失败: ' + error.message)
      } finally {
        this.isStarting = false
      }
    },

    async stopDownload() {
      this.isStopping = true

      try {
        const response = await this.$api.stopDownload()

        if (response.success) {
          this.$store.commit('setDownloading', false)
          this.isPaused = false
          this.$store.commit('addLog', '⏹️ 下载任务已停止')
          this.$message.success('下载任务已停止')
        } else {
          throw new Error(response.message || '停止下载失败')
        }
      } catch (error) {
        console.error('停止下载失败:', error)
        this.$message.error('停止下载失败: ' + error.message)
      } finally {
        this.isStopping = false
      }
    },

    async pauseDownload() {
      try {
        if (this.isPaused) {
          // 继续下载
          const response = await window.electronAPI.resumeDownload()
          if (response.success) {
            this.isPaused = false
            this.$store.commit('addLog', '▶️ 下载任务已从断点继续')
            this.$message.success('下载已从断点继续')
          } else {
            this.$message.warning(response.message || '未找到有效断点，请重新开始下载')
            this.$store.commit('addLog', '⚠️ ' + (response.message || '未找到有效断点'))
          }
        } else {
          // 暂停下载
          const response = await window.electronAPI.pauseDownload()
          if (response.success) {
            this.isPaused = true
            this.$store.commit('addLog', '⏸️ 下载任务已暂停，断点已保存')
            this.$message.success('下载已暂停，断点已保存')
          } else {
            throw new Error(response.message || '暂停失败')
          }
        }
      } catch (error) {
        const errorMessage = this.isPaused ? '继续下载失败' : '暂停下载失败'
        this.$message.error(`${errorMessage}: ${error.message}`)
        this.$store.commit('addLog', `❌ ${errorMessage}: ${error.message}`)
      }
    },

    getCurrentSettings() {
      let modeSettings = {}

      switch (this.downloadMode) {
        case 'following':
          modeSettings = this.followingSettings
          break
        case 'search':
          modeSettings = this.searchSettings
          break
        case 'ranking':
          modeSettings = this.rankingSettings
          break
        case 'artist':
          modeSettings = this.artistSettings
          break
        default:
          modeSettings = {}
      }

      // 添加全局设置
      return {
        ...modeSettings,
        classifyMode: this.classifyMode
      }
    },

    loadSettings() {
      // 从store加载保存的设置
      const saved = this.$store.state.downloadSettings
      console.log('📥 加载下载设置:', saved)

      if (saved) {
        this.downloadMode = saved.mode || 'following'
        this.classifyMode = saved.classifyMode || 'by_date' // 加载文件分类模式
        if (saved.following) {
          Object.assign(this.followingSettings, saved.following)
          console.log('📥 加载关注设置:', saved.following)
        }
        if (saved.search) {
          Object.assign(this.searchSettings, saved.search)
          console.log('📥 加载搜索设置:', saved.search)
        }
        if (saved.ranking) {
          Object.assign(this.rankingSettings, saved.ranking)
          console.log('📥 加载排行榜设置:', saved.ranking)
        }
        if (saved.artist) {
          Object.assign(this.artistSettings, saved.artist)
          console.log('📥 加载画师设置:', saved.artist)
        }
      }
    }
  },

  mounted() {
    // 组件挂载时加载保存的设置
    this.loadSettings()
  },

  watch: {
    downloadMode() {
      this.saveSettings()
    },

    // 监听store中的设置变化
    '$store.state.downloadSettings': {
      handler() {
        this.loadSettings()
      },
      deep: true
    }
  }
}
</script>

<style scoped>
.download-settings-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.range-input {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.range-separator {
  color: var(--text-muted);
  font-size: 14px;
}

.bookmark-filter {
  display: flex;
  align-items: center;
  gap: 10px;
}

.path-input {
  display: flex;
  gap: 10px;
}

.path-input .el-input {
  flex: 1;
}

.action-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
}

.control-btn {
  min-width: 120px;
  height: 40px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.start-btn {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  border: none;
  color: white;
}

.start-btn:hover {
  background: linear-gradient(135deg, #85ce61, #67c23a);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.stop-btn {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  border: none;
  color: white;
}

.stop-btn:hover {
  background: linear-gradient(135deg, #f78989, #f56c6c);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
}

.el-divider {
  margin: 15px 0;
}
</style>
