"""
配置工厂

统一创建和管理配置对象，提供配置的创建、合并、迁移等功能
"""

import json
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
import logging

from ..models.config import DownloadConfig, SpiderConfig
from .config_constants import ConfigTemplates, Compatibility, Environment
from .config_validator import ConfigValidator


class ConfigFactory:
    """配置工厂类"""
    
    def __init__(self):
        """初始化配置工厂"""
        self.logger = logging.getLogger(__name__)
        self.validator = ConfigValidator()
    
    def create_download_config(
        self, 
        data: Optional[Dict[str, Any]] = None,
        validate: bool = True
    ) -> DownloadConfig:
        """
        创建下载配置
        
        Args:
            data: 配置数据字典，如果为None则使用默认配置
            validate: 是否验证配置
            
        Returns:
            DownloadConfig: 下载配置对象
            
        Raises:
            ValueError: 配置验证失败时抛出
        """
        # 获取默认配置
        default_data = ConfigTemplates.get_default_download_config()
        
        # 合并用户配置
        if data:
            merged_data = self._merge_config(default_data, data)
        else:
            merged_data = default_data
        
        # 创建配置对象
        config = DownloadConfig.from_dict(merged_data)
        
        # 验证配置
        if validate:
            errors = self.validator.validate_download_config(config)
            if errors:
                error_msg = f"下载配置验证失败: {'; '.join(errors)}"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
        
        return config
    
    def create_spider_config(
        self, 
        data: Optional[Dict[str, Any]] = None,
        validate: bool = True
    ) -> SpiderConfig:
        """
        创建爬虫配置
        
        Args:
            data: 配置数据字典，如果为None则使用默认配置
            validate: 是否验证配置
            
        Returns:
            SpiderConfig: 爬虫配置对象
            
        Raises:
            ValueError: 配置验证失败时抛出
        """
        # 获取默认配置
        default_data = ConfigTemplates.get_default_spider_config()
        
        # 合并用户配置
        if data:
            merged_data = self._merge_config(default_data, data)
        else:
            merged_data = default_data
        
        # 应用环境特定配置
        merged_data = self._apply_environment_config(merged_data)
        
        # 创建配置对象
        config = SpiderConfig.from_dict(merged_data)
        
        # 验证配置
        if validate:
            errors = self.validator.validate_spider_config(config)
            if errors:
                error_msg = f"爬虫配置验证失败: {'; '.join(errors)}"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
        
        return config
    
    def create_from_file(
        self, 
        file_path: Union[str, Path],
        config_type: str = 'both',
        validate: bool = True
    ) -> Union[DownloadConfig, SpiderConfig, tuple]:
        """
        从文件创建配置
        
        Args:
            file_path: 配置文件路径
            config_type: 配置类型 ('download', 'spider', 'both')
            validate: 是否验证配置
            
        Returns:
            配置对象或配置对象元组
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 配置格式错误或验证失败
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件格式错误: {e}")
        
        # 检查并迁移配置
        data = self._migrate_config_if_needed(data)
        
        if config_type == 'download':
            download_data = data.get('download', {})
            return self.create_download_config(download_data, validate)
        
        elif config_type == 'spider':
            spider_data = data.get('spider', {})
            return self.create_spider_config(spider_data, validate)
        
        elif config_type == 'both':
            download_data = data.get('download', {})
            spider_data = data.get('spider', {})
            
            download_config = self.create_download_config(download_data, validate)
            spider_config = self.create_spider_config(spider_data, validate)
            
            return download_config, spider_config
        
        else:
            raise ValueError(f"不支持的配置类型: {config_type}")
    
    def merge_configs(
        self, 
        base_config: Union[DownloadConfig, SpiderConfig],
        override_data: Dict[str, Any],
        validate: bool = True
    ) -> Union[DownloadConfig, SpiderConfig]:
        """
        合并配置
        
        Args:
            base_config: 基础配置对象
            override_data: 覆盖数据
            validate: 是否验证配置
            
        Returns:
            合并后的配置对象
        """
        # 将基础配置转换为字典
        base_data = base_config.to_dict()
        
        # 合并配置
        merged_data = self._merge_config(base_data, override_data)
        
        # 创建新的配置对象
        if isinstance(base_config, DownloadConfig):
            return self.create_download_config(merged_data, validate)
        elif isinstance(base_config, SpiderConfig):
            return self.create_spider_config(merged_data, validate)
        else:
            raise ValueError(f"不支持的配置类型: {type(base_config)}")
    
    def create_preset_config(
        self, 
        preset_name: str,
        config_type: str = 'download'
    ) -> Union[DownloadConfig, SpiderConfig]:
        """
        创建预设配置
        
        Args:
            preset_name: 预设名称
            config_type: 配置类型
            
        Returns:
            配置对象
        """
        presets = self._get_config_presets()
        
        if preset_name not in presets:
            raise ValueError(f"未知的预设配置: {preset_name}")
        
        preset_data = presets[preset_name]
        
        if config_type == 'download':
            return self.create_download_config(preset_data.get('download', {}))
        elif config_type == 'spider':
            return self.create_spider_config(preset_data.get('spider', {}))
        else:
            raise ValueError(f"不支持的配置类型: {config_type}")
    
    def _merge_config(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """
        深度合并配置字典
        
        Args:
            base: 基础配置
            override: 覆盖配置
            
        Returns:
            合并后的配置
        """
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                # 递归合并嵌套字典
                result[key] = self._merge_config(result[key], value)
            else:
                # 直接覆盖
                result[key] = value
        
        return result
    
    def _apply_environment_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用环境特定配置
        
        Args:
            config_data: 配置数据
            
        Returns:
            应用环境配置后的数据
        """
        # 应用环境变量覆盖
        if Environment.is_development():
            config_data['log_level'] = Environment.get_log_level()
            # 开发环境下启用更详细的日志
            if config_data['log_level'] == 'DEBUG':
                config_data['selenium_headless'] = False  # 开发时显示浏览器
        
        return config_data
    
    def _migrate_config_if_needed(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        检查并迁移配置（如果需要）
        
        Args:
            data: 原始配置数据
            
        Returns:
            迁移后的配置数据
        """
        version = data.get('version', '1.0.0')
        
        if version in Compatibility.VERSION_MAPPING:
            if Compatibility.VERSION_MAPPING[version] == 'legacy':
                self.logger.info(f"检测到旧版本配置 {version}，正在迁移...")
                data = Compatibility.migrate_config(data, version)
                data['version'] = '6.0.0'  # 更新版本号
                self.logger.info("配置迁移完成")
        
        return data
    
    def _get_config_presets(self) -> Dict[str, Dict[str, Any]]:
        """
        获取配置预设
        
        Returns:
            预设配置字典
        """
        return {
            'high_quality': {
                'download': {
                    'min_bookmarks': 1000,
                    'skip_existing': True,
                    'create_info_file': True
                },
                'spider': {
                    'concurrent_downloads': 2,
                    'request_timeout': 60
                }
            },
            'fast_download': {
                'download': {
                    'skip_existing': True,
                    'create_info_file': False
                },
                'spider': {
                    'concurrent_downloads': 8,
                    'request_timeout': 15
                }
            },
            'safe_mode': {
                'download': {
                    'skip_existing': True,
                    'download_limit': 100
                },
                'spider': {
                    'concurrent_downloads': 1,
                    'request_timeout': 30,
                    'retry_attempts': 5
                }
            },
            'development': {
                'download': {
                    'download_limit': 10
                },
                'spider': {
                    'log_level': 'DEBUG',
                    'selenium_headless': False,
                    'concurrent_downloads': 1
                }
            }
        }
    
    def validate_config_file(self, file_path: Union[str, Path]) -> tuple[bool, List[str]]:
        """
        验证配置文件
        
        Args:
            file_path: 配置文件路径
            
        Returns:
            (是否有效, 错误列表)
        """
        errors = []
        
        try:
            download_config, spider_config = self.create_from_file(
                file_path, 'both', validate=True
            )
            return True, []
        except Exception as e:
            errors.append(str(e))
            return False, errors
    
    def export_config_template(self, file_path: Union[str, Path]) -> None:
        """
        导出配置模板
        
        Args:
            file_path: 导出文件路径
        """
        template_data = {
            'version': '6.0.0',
            'download': ConfigTemplates.get_default_download_config(),
            'spider': ConfigTemplates.get_default_spider_config(),
            '_comments': {
                'version': '配置文件版本',
                'download': '下载相关配置',
                'spider': '爬虫引擎配置'
            }
        }
        
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(template_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"配置模板已导出到: {file_path}")
