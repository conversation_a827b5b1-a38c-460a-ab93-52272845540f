"""
下载模式处理器模块

负责处理不同的下载模式（日期、排行榜、搜索、用户等）
"""

import logging
import time
from datetime import datetime, timedelta
from typing import List, Set

from ..models.artwork import Artwork
from ..models.config import DownloadConfig, DownloadMode, DateMode
from ..models.exceptions import PixivSpiderError
from ..utils.selenium_utils import SeleniumDriver
from ..processors.artwork_processor import ArtworkProcessor


class DownloadModeHandler:
    """下载模式处理器"""

    def __init__(self, download_config: DownloadConfig, selenium_driver: SeleniumDriver,
                 artwork_processor: ArtworkProcessor):
        """
        初始化下载模式处理器

        Args:
            download_config: 下载配置
            selenium_driver: Selenium驱动器
            artwork_processor: 作品处理器
        """
        self.logger = logging.getLogger(__name__)
        self.download_config = download_config
        self.selenium_driver = selenium_driver
        self.artwork_processor = artwork_processor

        # 资源清理标志
        self._is_cleaned_up = False

    def get_artworks_by_mode(self) -> List[Artwork]:
        """根据下载模式获取作品列表"""
        mode = self.download_config.download_mode
        self.logger.info(f"🎯 开始获取作品列表 - 当前下载模式: {mode}")

        # 支持字符串和枚举值的比较
        mode_value = mode.value if hasattr(mode, 'value') else str(mode)
        self.logger.info(f"📋 模式值: {mode_value}")

        if mode_value == "date" or mode == DownloadMode.DATE:
            self.logger.info("📅 使用日期模式获取作品")
            return self.get_artworks_by_date()
        elif mode_value == "ranking" or mode == DownloadMode.RANKING:
            self.logger.info("🏆 使用排行榜模式获取作品")
            return self.get_artworks_by_ranking()
        elif mode_value == "search" or mode == DownloadMode.SEARCH:
            self.logger.info("🔍 使用搜索模式获取作品")
            return self.get_artworks_by_search()
        elif mode_value == "user" or mode == DownloadMode.USER:
            self.logger.info("👤 使用用户模式获取作品")
            return self.get_artworks_by_user()
        elif mode_value == "bookmark" or mode == DownloadMode.BOOKMARK:
            self.logger.info("⭐ 使用收藏模式获取作品")
            return self.get_artworks_by_bookmark()
        elif mode_value == "follow" or mode == DownloadMode.FOLLOW or mode_value == "following":
            self.logger.info("👥 使用关注模式获取作品")
            return self.get_artworks_by_follow()
        else:
            error_msg = f"不支持的下载模式: {mode} (值: {mode_value})"
            self.logger.error(f"❌ {error_msg}")
            raise PixivSpiderError(error_msg)

    def get_artworks_by_date(self) -> List[Artwork]:
        """按日期获取作品 - 从关注画师新作品页面收集"""
        try:
            self.logger.info("📅 开始按日期获取作品")

            # 根据子模式选择不同的处理逻辑
            date_mode = getattr(self.download_config, 'date_mode', DateMode.BY_DATE_RANGE)
            self.logger.info(f"📋 日期子模式: {date_mode}")

            # 支持字符串和枚举值的比较
            date_mode_value = date_mode.value if hasattr(date_mode, 'value') else str(date_mode)
            self.logger.info(f"🔧 日期模式值: {date_mode_value}")

            if date_mode_value == "by_page_range" or date_mode == DateMode.BY_PAGE_RANGE:
                self.logger.info("📄 使用页码范围模式")
                return self.get_artworks_by_page_range()
            else:  # DateMode.BY_DATE_RANGE
                self.logger.info("📆 使用日期范围模式")
                return self.get_artworks_by_date_range()

        except Exception as e:
            self.logger.error(f"❌ 按日期获取作品失败: {e}")
            import traceback
            self.logger.error(f"💥 错误详情: {traceback.format_exc()}")
            return []

    def get_artworks_by_page_range(self) -> List[Artwork]:
        """按页码范围下载 - 下载指定页码范围内的所有作品"""
        # 生成关注画师新作品页面URL列表
        page_urls = []
        for page in range(self.download_config.start_page, self.download_config.end_page + 1):
            url = f"https://www.pixiv.net/bookmark_new_illust.php?p={page}"
            page_urls.append(url)

        self.logger.info(f"按页码范围模式: 准备从关注画师新作品页面采集 {len(page_urls)} 个页面...")
        self.logger.info(f"页面URL列表: {page_urls}")

        # 获取作品链接 - 启用快速模式
        self.logger.info("🚀 开始调用selenium_driver.get_page_links_batch...")
        batch_start_time = time.time()

        try:
            artwork_links = self.selenium_driver.get_page_links_batch(page_urls, fast_mode=True)
            batch_time = time.time() - batch_start_time
            self.logger.info(f"✅ get_page_links_batch调用完成 (耗时: {batch_time:.2f}秒)")
        except Exception as e:
            batch_time = time.time() - batch_start_time
            self.logger.error(f"❌ get_page_links_batch调用失败 (耗时: {batch_time:.2f}秒): {e}")
            import traceback
            self.logger.error(f"💥 错误详情: {traceback.format_exc()}")
            return []

        self.logger.info(f"📊 采集到 {len(artwork_links)} 个作品链接")

        if not artwork_links:
            self._log_no_artworks_warning()
            return []

        # 并行处理作品链接（不进行日期过滤）
        artworks = self.artwork_processor.process_artwork_links_parallel(artwork_links)

        self.logger.info(f"按页码范围模式: 成功处理 {len(artworks)} 个作品")
        return artworks

    def get_artworks_by_date_range(self) -> List[Artwork]:
        """按日期范围下载 - 在页码范围内按日期过滤作品"""
        # 计算日期范围
        today = datetime.now().date()
        target_dates = []
        for i in range(self.download_config.days):
            target_date = today - timedelta(days=i)
            target_dates.append(target_date)

        self.logger.info(f"按日期范围模式: 目标下载日期: {[d.strftime('%Y-%m-%d') for d in target_dates]}")

        # 生成关注画师新作品页面URL列表
        page_urls = []
        for page in range(self.download_config.start_page, self.download_config.end_page + 1):
            url = f"https://www.pixiv.net/bookmark_new_illust.php?p={page}"
            page_urls.append(url)

        self.logger.info(f"📊 按日期范围模式: 准备从关注画师新作品页面采集 {len(page_urls)} 个页面...")

        # 获取作品链接 - 启用快速模式
        self.logger.info("🔗 开始获取作品链接...")
        artwork_links = self.selenium_driver.get_page_links_batch(page_urls, fast_mode=True)

        self.logger.info(f"✅ 采集到 {len(artwork_links)} 个作品链接")

        if not artwork_links:
            self._log_no_artworks_warning()
            return []

        # 并行处理作品链接并进行日期过滤
        self.logger.info(f"⚙️ 开始并行处理 {len(artwork_links)} 个作品链接...")
        all_artworks = self.artwork_processor.process_artwork_links_parallel(artwork_links)

        # 进行日期过滤
        artworks = []
        processed_count = len(all_artworks)
        filtered_count = 0

        for artwork in all_artworks:
            self.logger.debug(f"处理作品 {artwork.id}: {artwork.title}, 上传日期: {artwork.upload_date}")

            # 检查日期过滤
            if artwork.upload_date:
                # 将artwork的上传日期转换为本地日期（忽略时区）
                if hasattr(artwork.upload_date, 'date'):
                    artwork_date = artwork.upload_date.date()
                else:
                    artwork_date = artwork.upload_date

                # 检查是否在目标日期列表中
                if artwork_date in target_dates:
                    artworks.append(artwork)
                    self.logger.debug(f"作品 {artwork.id} 通过日期过滤: {artwork_date}")
                else:
                    filtered_count += 1
                    self.logger.debug(f"作品 {artwork.id} 被日期过滤: {artwork_date} 不在目标日期 {target_dates} 中")
            else:
                # 如果没有上传日期信息，也包含该作品（可能是数据解析问题）
                artworks.append(artwork)
                self.logger.warning(f"作品 {artwork.id} 没有上传日期信息，但仍包含在结果中")

        self.logger.info(f"📊 按日期范围模式: 成功解析 {processed_count} 个作品, 日期过滤掉 {filtered_count} 个")
        self.logger.info(f"🎯 找到 {len(artworks)} 个符合日期条件的作品")
        return artworks

    def _log_no_artworks_warning(self):
        """记录没有获取到作品的警告信息"""
        self.logger.warning("⚠️ 没有获取到任何作品链接，可能的原因：")
        self.logger.warning("1️⃣ 您还没有关注任何画师")
        self.logger.warning("2️⃣ 关注的画师最近没有发布新作品")
        self.logger.warning("3️⃣ 登录状态可能已过期，请尝试重新登录")
        self.logger.warning("4️⃣ 页面加载失败或网络问题")

    def get_artworks_by_ranking(self) -> List[Artwork]:
        """按排行榜获取作品 - 使用Selenium访问排行榜页面"""
        try:
            ranking_config = self.download_config.ranking_config

            # 验证排行榜配置
            config_errors = ranking_config.validate_config()
            if config_errors:
                error_msg = "排行榜配置错误: " + "; ".join(config_errors)
                self.logger.error(error_msg)
                raise PixivSpiderError(error_msg)

            self.logger.info(f"准备采集排行榜: {ranking_config.category.value}-{ranking_config.rating.value}-{ranking_config.period.value}")

            # 排行榜模式固定使用1-2页
            ranking_start_page = 1
            ranking_end_page = 2
            self.logger.info(f"排行榜模式使用固定页码范围: {ranking_start_page}-{ranking_end_page}")

            # 生成排行榜页面URL列表
            page_urls = []
            for page in range(ranking_start_page, ranking_end_page + 1):
                # 使用新的URL构建方法
                url = ranking_config.get_ranking_url(page=page, date=ranking_config.specific_date)
                page_urls.append(url)
                self.logger.debug(f"排行榜URL: {url}")

            self.logger.info(f"准备从排行榜页面采集 {len(page_urls)} 个页面...")

            # 获取作品链接
            artwork_links = self.selenium_driver.get_page_links_batch(page_urls)

            self.logger.info(f"采集到 {len(artwork_links)} 个排行榜作品链接")

            if not artwork_links:
                self.logger.warning("没有获取到任何排行榜作品链接，可能的原因：")
                self.logger.warning("1. 排行榜分类参数错误")
                self.logger.warning("2. 指定日期没有排行榜数据")
                self.logger.warning("3. 网络问题或页面加载失败")
                self.logger.warning("4. 排行榜页面访问限制")
                if ranking_config.rating.value == "R-18":
                    self.logger.warning("5. R18内容需要确认登录状态和年龄验证")
                return []

            # 处理每个作品链接
            artworks = []
            for link in artwork_links:
                artwork = self.artwork_processor.process_artwork_link(link)
                if artwork:
                    artworks.append(artwork)

            self.logger.info(f"成功处理 {len(artworks)} 个排行榜作品")
            return artworks

        except Exception as e:
            self.logger.error(f"按排行榜获取作品失败: {e}")
            return []

    def get_artworks_by_search(self) -> List[Artwork]:
        """按搜索获取作品 - 使用页码范围下载"""
        try:
            keyword = self.download_config.search_keyword
            if not keyword:
                raise PixivSpiderError("搜索模式下必须提供搜索关键词")

            # 搜索模式直接使用页码范围下载
            return self._get_artworks_by_search_page_range(keyword)

        except Exception as e:
            self.logger.error(f"按搜索获取作品失败: {e}")
            return []

    def _get_artworks_by_search_page_range(self, keyword: str) -> List[Artwork]:
        """按页码范围搜索下载 - 使用新的URL生成逻辑"""
        # 生成搜索页面URL列表
        page_urls = []
        search_config = self.download_config.search_config

        for page in range(self.download_config.start_page, self.download_config.end_page + 1):
            url = search_config.get_search_url(keyword, page)
            page_urls.append(url)

        self.logger.info(f"📊 按页码范围搜索模式: 准备从搜索页面采集 {len(page_urls)} 个页面...")
        self.logger.info(f"🔍 搜索关键词: {keyword}")
        self.logger.info(f"📂 搜索种类: {search_config.category.value}")
        self.logger.info(f"⭐ 收藏过滤: {search_config.bookmark_count.value if search_config.bookmark_count.value != -1 else '不启用'}")

        # 获取作品链接
        self.logger.info("🔗 开始获取搜索结果链接...")
        artwork_links = self.selenium_driver.get_page_links_batch(page_urls)

        self.logger.info(f"✅ 采集到 {len(artwork_links)} 个作品链接")

        if not artwork_links:
            self.logger.warning("⚠️ 没有获取到任何作品链接，可能的原因：")
            self.logger.warning("1️⃣ 搜索关键词没有匹配的作品")
            self.logger.warning("2️⃣ 收藏过滤条件过于严格")
            self.logger.warning("3️⃣ 页面加载失败或网络问题")
            self.logger.warning("4️⃣ 搜索关键词需要为英文或日文")
            return []

        # 并行处理作品链接（不进行额外过滤）
        artworks = self.artwork_processor.process_artwork_links_parallel(artwork_links)

        self.logger.info(f"按页码范围搜索模式: 成功处理 {len(artworks)} 个作品")
        return artworks

    def get_artworks_by_user(self) -> List[Artwork]:
        """按用户获取作品"""
        try:
            user_id = self.download_config.user_id
            if not user_id:
                raise PixivSpiderError("用户模式下必须提供用户ID")

            pages = self.download_config.end_page - self.download_config.start_page + 1
            # 传递download_config以使用新的URL生成方法
            artwork_links = self.selenium_driver.get_user_artworks(user_id, pages, self.download_config)

            # 并行处理作品链接
            artworks = self.artwork_processor.process_artwork_links_parallel(artwork_links)

            return artworks

        except Exception as e:
            self.logger.error(f"按用户获取作品失败: {e}")
            return []

    def get_artworks_by_bookmark(self) -> List[Artwork]:
        """按收藏获取作品"""
        # TODO: 实现收藏模式
        self.logger.warning("收藏模式尚未实现")
        return []

    def get_artworks_by_follow(self) -> List[Artwork]:
        """按关注获取作品 - 实际上就是按日期模式（关注画师新作品页面）"""
        try:
            self.logger.info("🎯 关注模式：从关注画师新作品页面获取作品")

            # 关注模式实际上就是按日期模式，从关注画师新作品页面收集
            # 这与前端的"关注画师作品"模式对应
            return self.get_artworks_by_date()

        except Exception as e:
            self.logger.error(f"❌ 关注模式获取作品失败: {e}")
            import traceback
            self.logger.error(f"💥 错误详情: {traceback.format_exc()}")
            return []

    def cleanup_resources(self) -> None:
        """清理下载模式处理器的资源"""
        if self._is_cleaned_up:
            return

        try:
            self.logger.info("🧹 开始清理下载模式处理器资源...")

            # 清理对象引用
            self.download_config = None
            self.selenium_driver = None
            self.artwork_processor = None

            # 强制垃圾回收
            import gc
            gc.collect()

            self._is_cleaned_up = True
            self.logger.info("✅ 下载模式处理器资源清理完成")

        except Exception as e:
            self.logger.error(f"❌ 清理下载模式处理器资源失败: {e}")

    def __del__(self):
        """析构函数，确保资源被清理"""
        try:
            self.cleanup_resources()
        except Exception:
            # 析构函数中不应该抛出异常
            pass
