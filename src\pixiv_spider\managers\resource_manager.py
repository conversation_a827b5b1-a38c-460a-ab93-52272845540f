"""
资源管理器模块

负责管理和清理各种资源，包括Selenium驱动器、API服务、缓存等
"""

import logging
import subprocess
import platform
from typing import Optional, Callable

from ..utils.selenium_utils import SeleniumDriver
from ..interfaces.api_interface import IApiService
from ..interfaces.download_interface import IDownloadService
from ..utils.cache_manager import CacheManager


class ResourceManager:
    """资源管理器"""

    def __init__(self):
        """初始化资源管理器"""
        self.logger = logging.getLogger(__name__)
        
        # 资源引用
        self.selenium_driver: Optional[SeleniumDriver] = None
        self.api_service: Optional[IApiService] = None
        self.download_service: Optional[IDownloadService] = None
        self.cache_manager: Optional[CacheManager] = None
        
        # 回调函数
        self._progress_callback: Optional[Callable] = None
        self._status_callback: Optional[Callable] = None
        
        # 状态标志
        self._is_running = False

        # 延迟清理线程跟踪
        self._cleanup_thread = None

    def register_selenium_driver(self, selenium_driver: SeleniumDriver):
        """注册Selenium驱动器"""
        self.selenium_driver = selenium_driver

    def register_api_service(self, api_service: IApiService):
        """注册API服务"""
        self.api_service = api_service

    def register_download_service(self, download_service: IDownloadService):
        """注册下载服务"""
        self.download_service = download_service

    def register_cache_manager(self, cache_manager: CacheManager):
        """注册缓存管理器"""
        self.cache_manager = cache_manager

    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self._progress_callback = callback

    def set_status_callback(self, callback: Callable):
        """设置状态回调函数"""
        self._status_callback = callback

    def set_running_state(self, is_running: bool):
        """设置运行状态"""
        self._is_running = is_running

    def cleanup_selenium_after_download(self) -> None:
        """下载完成后清理Selenium资源"""
        try:
            if self.selenium_driver:
                self.logger.info("🌐 正在清理Selenium驱动器...")
                self.selenium_driver.quit()
                self.selenium_driver = None
                self.logger.info("✅ Selenium驱动器已清理")

                # Selenium驱动器已经在quit()方法中精确清理了进程
                # 不需要额外的全局进程清理
            else:
                self.logger.info("ℹ️ Selenium驱动器不存在，无需清理")

        except Exception as e:
            self.logger.error(f"❌ 清理Selenium资源失败: {e}")

    def cleanup_all_resources(self) -> None:
        """清理所有资源"""
        try:
            self.logger.info("🧹 开始清理所有资源...")

            # 设置停止标志
            self._is_running = False

            # 清理下载服务
            if self.download_service:
                self.logger.info("📥 正在清理下载服务...")
                if hasattr(self.download_service, 'cleanup_resources'):
                    self.download_service.cleanup_resources()
                self.logger.info("✅ 下载服务已清理")

            # 清理Selenium驱动器
            if self.selenium_driver:
                self.logger.info("🌐 正在清理Selenium驱动器...")
                self.selenium_driver.quit()
                self.selenium_driver = None
                self.logger.info("✅ Selenium驱动器已清理")

            # 清理API服务会话池
            if self.api_service:
                self.logger.info("🔗 正在清理API服务...")
                try:
                    # 正确清理API服务的会话池
                    if hasattr(self.api_service, 'cleanup_sessions'):
                        self.api_service.cleanup_sessions()
                    elif hasattr(self.api_service, 'session_pool'):
                        # 如果没有cleanup_sessions方法，手动清理会话池
                        while not self.api_service.session_pool.empty():
                            try:
                                session = self.api_service.session_pool.get_nowait()
                                session.close()
                            except Exception:
                                break
                    elif hasattr(self.api_service, 'session') and self.api_service.session:
                        # 单个会话的情况
                        self.api_service.session.close()
                except Exception as e:
                    self.logger.debug(f"关闭API服务会话失败: {e}")

                self.api_service = None
                self.logger.info("✅ API服务已清理")

            # 清理缓存
            if self.cache_manager:
                self.logger.info("📦 正在清理缓存...")
                self.cache_manager.clear_all_caches()
                self.logger.info("✅ 缓存已清理")

            # 清理回调函数
            try:
                self._progress_callback = None
                self._status_callback = None
                self.logger.info("🔗 回调函数已重置")
            except Exception as e:
                self.logger.debug(f"重置回调函数失败: {e}")

            # 清理延迟清理线程
            if self._cleanup_thread and self._cleanup_thread.is_alive():
                self.logger.info("⏹️ 等待延迟清理线程结束...")
                # 不强制终止线程，让它自然结束
                self._cleanup_thread = None

            # 清理所有对象引用
            try:
                # 清理服务引用
                self.download_service = None
                self.selenium_driver = None
                self.api_service = None
                self.cache_manager = None

                # 清理回调函数引用
                self._progress_callback = None
                self._status_callback = None

                self.logger.debug("🗑️ 所有对象引用已清理")
            except Exception as e:
                self.logger.debug(f"清理对象引用失败: {e}")

            # 强制垃圾回收
            import gc
            gc.collect()
            self.logger.debug("🗑️ 强制垃圾回收已执行")

            self.logger.info("✅ 所有资源清理完成")

        except Exception as e:
            self.logger.error(f"❌ 清理资源时出错: {e}")
            import traceback
            self.logger.debug(f"清理异常堆栈: {traceback.format_exc()}")

    def __del__(self):
        """析构函数，确保资源被清理"""
        try:
            self.cleanup_all_resources()
        except Exception:
            # 析构函数中不应该抛出异常
            pass

    def reset_for_new_download(self, service_provider, cookies) -> None:
        """为新的下载任务重置状态"""
        try:
            self.logger.info("重置资源状态...")

            # 确保不在运行状态
            self._is_running = False

            # 重新初始化服务（如果需要）
            if not self.api_service and cookies:
                service_provider.setup_with_cookies(cookies)
                self.api_service = service_provider.get_api_service()

            if not self.download_service and self.api_service:
                self.download_service = service_provider.get_download_service()
                # 设置回调
                if hasattr(self.download_service, 'set_progress_callback'):
                    self.download_service.set_progress_callback(self._progress_callback)
                if hasattr(self.download_service, 'set_status_callback'):
                    self.download_service.set_status_callback(self._status_callback)
            elif self.download_service:
                # 重置下载服务状态
                if hasattr(self.download_service, 'reset_state'):
                    self.download_service.reset_state()
                    self.logger.info("下载服务状态已重置")

                # 重新设置回调（确保不会丢失）
                if hasattr(self.download_service, 'set_progress_callback') and self._progress_callback:
                    self.download_service.set_progress_callback(self._progress_callback)
                    self.logger.info("✅ 重新设置进度回调")
                if hasattr(self.download_service, 'set_status_callback') and self._status_callback:
                    self.download_service.set_status_callback(self._status_callback)
                    self.logger.info("✅ 重新设置状态回调")
            
            self.logger.info("资源状态重置完成")
            
        except Exception as e:
            self.logger.error(f"重置资源状态时出错: {e}")

    def stop_all_services(self) -> None:
        """立即停止所有服务，但保留Selenium驱动器直到任务完全停止"""
        if self._is_running:
            self.logger.info("🛑 收到停止信号，立即停止所有服务...")
            self._is_running = False

            # 立即通知状态更新
            self._notify_status("正在立即停止下载...")

            # 立即停止下载服务（不立即清理，让stop_download自己处理）
            if self.download_service and hasattr(self.download_service, 'stop_download'):
                self.download_service.stop_download()

            self.logger.info("✅ 下载服务停止信号已发送")

            # 延迟清理所有资源，给停止过程时间完成
            self._schedule_delayed_cleanup()

    def pause_all_services(self) -> None:
        """暂停所有服务，保存断点"""
        if self._is_running:
            self.logger.info("⏸️ 收到暂停信号，暂停所有服务...")

            # 通知状态更新
            self._notify_status("正在暂停下载...")

            # 暂停下载服务
            if self.download_service and hasattr(self.download_service, 'pause_download'):
                self.download_service.pause_download()

            self.logger.info("✅ 下载服务暂停信号已发送")

    def resume_all_services(self) -> bool:
        """继续所有服务，从断点恢复"""
        self.logger.info("▶️ 收到继续信号，恢复所有服务...")

        # 通知状态更新
        self._notify_status("正在从断点继续下载...")

        # 继续下载服务
        if self.download_service and hasattr(self.download_service, 'resume_download'):
            success = self.download_service.resume_download()
            if success:
                self._is_running = True
                self.logger.info("✅ 下载服务已从断点继续")
                return True
            else:
                self.logger.warning("❌ 下载服务继续失败")
                return False

        return False

    def _schedule_delayed_cleanup(self) -> None:
        """延迟清理所有资源，给停止过程时间完成"""
        import threading
        import time

        # 如果已有清理线程在运行，先停止它
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            self.logger.info("⏹️ 取消之前的延迟清理任务")
            # 设置标志让之前的线程退出（通过检查_is_running状态）

        def delayed_cleanup():
            try:
                # 等待2秒，让停止过程完成
                time.sleep(2)

                # 检查是否有新的下载任务开始
                if not self._is_running:
                    self.logger.info("🧹 开始延迟清理资源...")

                    # 清理下载服务（如果还没清理）
                    self._cleanup_download_service_only()

                    # 清理Selenium驱动器
                    if self.selenium_driver:
                        self.logger.info("🧹 延迟清理Selenium驱动器...")
                        try:
                            self.selenium_driver.quit()
                            self.selenium_driver = None
                            self.logger.info("✅ Selenium驱动器延迟清理完成")
                        except Exception as e:
                            self.logger.error(f"❌ 延迟清理Selenium驱动器失败: {e}")

                    self.logger.info("✅ 延迟清理完成")
                else:
                    self.logger.info("ℹ️ 检测到新任务，跳过延迟清理")

            except Exception as e:
                self.logger.error(f"❌ 延迟清理任务异常: {e}")
            finally:
                # 清理线程引用
                self._cleanup_thread = None

        # 在后台线程中执行延迟清理
        self._cleanup_thread = threading.Thread(target=delayed_cleanup, daemon=True)
        self._cleanup_thread.start()
        self.logger.info("⏰ 已启动延迟清理任务（2秒后执行）")

    def _cleanup_download_service_only(self) -> None:
        """只清理下载服务，保留Selenium驱动器"""
        try:
            # 清理下载服务
            if self.download_service:
                self.logger.info("📥 正在清理下载服务...")
                if hasattr(self.download_service, 'cleanup_resources'):
                    self.download_service.cleanup_resources()
                self.logger.info("✅ 下载服务已清理")

            # 清理API服务会话池
            if self.api_service:
                self.logger.info("🔗 正在清理API服务...")
                try:
                    # 正确清理API服务的会话池
                    if hasattr(self.api_service, 'cleanup_sessions'):
                        self.api_service.cleanup_sessions()
                    elif hasattr(self.api_service, 'session_pool'):
                        # 如果没有cleanup_sessions方法，手动清理会话池
                        while not self.api_service.session_pool.empty():
                            try:
                                session = self.api_service.session_pool.get_nowait()
                                session.close()
                            except Exception:
                                break
                    elif hasattr(self.api_service, 'session') and self.api_service.session:
                        # 单个会话的情况
                        self.api_service.session.close()
                except Exception as e:
                    self.logger.debug(f"关闭API服务会话失败: {e}")

                self.api_service = None
                self.logger.info("✅ API服务已清理")

            # 清理缓存
            if self.cache_manager:
                self.logger.info("📦 正在清理缓存...")
                self.cache_manager.clear_all_caches()
                self.logger.info("✅ 缓存已清理")

            # 清理回调函数
            try:
                self._progress_callback = None
                self._status_callback = None
                self.logger.info("🔗 回调函数已重置")
            except Exception as e:
                self.logger.debug(f"重置回调函数失败: {e}")

            self.logger.info("✅ 下载服务资源清理完成（Selenium驱动器保留）")

        except Exception as e:
            self.logger.error(f"❌ 清理下载服务资源时出错: {e}")
            import traceback
            self.logger.debug(f"清理异常堆栈: {traceback.format_exc()}")

    def _notify_progress(self, current: int, total: int, message: str = "") -> None:
        """通知进度更新"""
        if self._progress_callback:
            try:
                self._progress_callback(current, total, message)
            except Exception as e:
                self.logger.error(f"进度回调函数执行失败: {e}")

    def _notify_status(self, message: str) -> None:
        """通知状态更新"""
        if self._status_callback:
            try:
                self._status_callback(message)
            except Exception as e:
                self.logger.error(f"状态回调函数执行失败: {e}")

    def get_resource_stats(self) -> dict:
        """获取资源状态统计"""
        stats = {
            "is_running": self._is_running,
            "selenium_driver_active": self.selenium_driver is not None,
            "api_service_active": self.api_service is not None,
            "download_service_active": self.download_service is not None,
            "cache_manager_active": self.cache_manager is not None
        }
        
        # 添加缓存统计
        if self.cache_manager:
            stats.update(self.cache_manager.get_cache_stats())
            
        return stats

    def __enter__(self):
        """支持上下文管理器"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """支持上下文管理器"""
        self.cleanup_all_resources()
        # 不抑制异常
        return False
