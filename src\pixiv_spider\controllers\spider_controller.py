"""
爬虫控制器

协调各个服务之间的调用，实现高级业务逻辑
"""

import logging
from typing import List, Optional, Dict, Any
from ..core.pixiv_spider import PixivSpider
from ..container.service_container import ServiceContainer
from ..models.artwork import Artwork
from ..models.config import DownloadConfig, SpiderConfig


class SpiderController:
    """爬虫控制器类"""

    def __init__(self, container: Optional[ServiceContainer] = None):
        """
        初始化爬虫控制器

        Args:
            container: 服务容器
        """
        self.container = container or ServiceContainer()
        self.spider = PixivSpider(self.container)
        self.config_manager = self.container.get_config_manager()
        self.logger = logging.getLogger(__name__)
    
    def start_download_task(self, download_config: DownloadConfig) -> Dict[str, Any]:
        """
        启动下载任务
        
        Args:
            download_config: 下载配置
            
        Returns:
            Dict: 下载结果统计
        """
        try:
            # 更新配置
            self.spider.update_download_config(download_config)
            
            # 执行下载
            stats = self.spider.start_download()
            
            return stats
            
        except Exception as e:
            self.logger.error(f"下载任务执行失败: {e}")
            raise
    
    def stop_download_task(self) -> None:
        """停止下载任务"""
        self.spider.stop_download()

    def pause_download_task(self) -> None:
        """暂停下载任务"""
        self.spider.pause_download()

    def resume_download_task(self) -> bool:
        """继续下载任务"""
        return self.spider.resume_download()

    def get_download_status(self) -> Dict[str, Any]:
        """
        获取下载状态
        
        Returns:
            Dict: 状态信息
        """
        return self.spider.get_download_stats()
    
    def authenticate_user(self) -> bool:
        """
        用户认证
        
        Returns:
            bool: 是否认证成功
        """
        return self.spider.authenticate()
    
    def interactive_login(self) -> bool:
        """
        交互式登录
        
        Returns:
            bool: 是否登录成功
        """
        return self.spider.interactive_login()
    
    def set_progress_callback(self, callback) -> None:
        """设置进度回调"""
        self.spider.set_progress_callback(callback)
    
    def set_status_callback(self, callback) -> None:
        """设置状态回调"""
        self.spider.set_status_callback(callback)

    def cleanup(self):
        """清理资源"""
        try:
            self.logger.info("正在清理SpiderController资源...")

            if self.spider:
                # 清理selenium驱动器
                if hasattr(self.spider, 'selenium_driver') and self.spider.selenium_driver:
                    try:
                        self.spider.selenium_driver.quit()
                        self.logger.info("Selenium驱动器已清理")
                    except Exception as e:
                        self.logger.warning(f"清理Selenium驱动器失败: {e}")

                # 清理其他资源
                if hasattr(self.spider, 'cleanup'):
                    self.spider.cleanup()

                self.spider = None

            self.logger.info("SpiderController资源清理完成")

        except Exception as e:
            self.logger.error(f"SpiderController资源清理失败: {e}")