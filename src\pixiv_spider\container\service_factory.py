"""
服务工厂

提供复杂服务的创建和配置逻辑
"""

import logging
from typing import Dict, Any, Optional
from .service_container import ServiceContainer
from ..services.auth_service import AuthService
from ..services.pixiv_api_service import PixivApiService
from ..services.download_service import DownloadService
from ..services.cache_service import CacheService


class ServiceFactory:
    """服务工厂 - 处理复杂的服务创建逻辑"""
    
    def __init__(self, container: ServiceContainer):
        """
        初始化服务工厂
        
        Args:
            container: 服务容器
        """
        self.container = container
        self.logger = logging.getLogger(__name__)
    
    def create_auth_service(self) -> AuthService:
        """
        创建认证服务
        
        Returns:
            AuthService: 认证服务实例
        """
        try:
            config_manager = self.container.get_config_manager()
            auth_service = AuthService(config_manager)
            self.logger.debug("✅ 认证服务创建成功")
            return auth_service
        except Exception as e:
            self.logger.error(f"❌ 创建认证服务失败: {e}")
            raise
    
    def create_api_service(self, cookies: Dict[str, Any]) -> PixivApiService:
        """
        创建API服务
        
        Args:
            cookies: 认证cookies
            
        Returns:
            PixivApiService: API服务实例
        """
        try:
            config_manager = self.container.get_config_manager()
            api_service = PixivApiService(cookies, config_manager)
            self.logger.debug("✅ API服务创建成功")
            return api_service
        except Exception as e:
            self.logger.error(f"❌ 创建API服务失败: {e}")
            raise
    
    def create_download_service(self, api_service: PixivApiService) -> DownloadService:
        """
        创建下载服务
        
        Args:
            api_service: API服务实例
            
        Returns:
            DownloadService: 下载服务实例
        """
        try:
            config_manager = self.container.get_config_manager()
            download_service = DownloadService(api_service, config_manager)
            self.logger.debug("✅ 下载服务创建成功")
            return download_service
        except Exception as e:
            self.logger.error(f"❌ 创建下载服务失败: {e}")
            raise
    
    def create_cache_service(self) -> CacheService:
        """
        创建缓存服务
        
        Returns:
            CacheService: 缓存服务实例
        """
        try:
            config_manager = self.container.get_config_manager()
            cache_service = CacheService(config_manager)
            self.logger.debug("✅ 缓存服务创建成功")
            return cache_service
        except Exception as e:
            self.logger.error(f"❌ 创建缓存服务失败: {e}")
            raise
    
    def create_service_chain(self, cookies: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建完整的服务链
        
        Args:
            cookies: 认证cookies
            
        Returns:
            Dict[str, Any]: 服务实例字典
        """
        try:
            self.logger.info("🔧 开始创建服务链...")
            
            # 创建认证服务
            auth_service = self.create_auth_service()
            
            # 创建API服务
            api_service = self.create_api_service(cookies)
            
            # 创建下载服务
            download_service = self.create_download_service(api_service)
            
            # 创建缓存服务
            cache_service = self.create_cache_service()
            
            services = {
                'auth_service': auth_service,
                'api_service': api_service,
                'download_service': download_service,
                'cache_service': cache_service
            }
            
            self.logger.info("✅ 服务链创建完成")
            return services
            
        except Exception as e:
            self.logger.error(f"❌ 创建服务链失败: {e}")
            raise


class ServiceProvider:
    """服务提供者 - 统一的服务访问接口"""
    
    def __init__(self, container: ServiceContainer):
        """
        初始化服务提供者
        
        Args:
            container: 服务容器
        """
        self.container = container
        self.factory = ServiceFactory(container)
        self.logger = logging.getLogger(__name__)
        
        # 服务缓存
        self._service_cache: Dict[str, Any] = {}
    
    def get_auth_service(self) -> AuthService:
        """获取认证服务"""
        if 'auth_service' not in self._service_cache:
            self._service_cache['auth_service'] = self.factory.create_auth_service()
        return self._service_cache['auth_service']
    
    def get_cache_service(self) -> CacheService:
        """获取缓存服务"""
        if 'cache_service' not in self._service_cache:
            self._service_cache['cache_service'] = self.factory.create_cache_service()
        return self._service_cache['cache_service']
    
    def get_api_service(self, cookies: Optional[Dict[str, Any]] = None) -> Optional[PixivApiService]:
        """
        获取API服务
        
        Args:
            cookies: 认证cookies（首次创建时需要）
            
        Returns:
            Optional[PixivApiService]: API服务实例
        """
        if 'api_service' not in self._service_cache:
            if cookies is None:
                self.logger.warning("⚠️ 获取API服务需要提供cookies")
                return None
            self._service_cache['api_service'] = self.factory.create_api_service(cookies)
        return self._service_cache['api_service']
    
    def get_download_service(self, api_service: Optional[PixivApiService] = None) -> Optional[DownloadService]:
        """
        获取下载服务
        
        Args:
            api_service: API服务实例（首次创建时需要）
            
        Returns:
            Optional[DownloadService]: 下载服务实例
        """
        if 'download_service' not in self._service_cache:
            if api_service is None:
                api_service = self._service_cache.get('api_service')
                if api_service is None:
                    self.logger.warning("⚠️ 获取下载服务需要提供API服务")
                    return None
            self._service_cache['download_service'] = self.factory.create_download_service(api_service)
        return self._service_cache['download_service']
    
    def setup_with_cookies(self, cookies: Dict[str, Any]) -> None:
        """
        使用cookies设置服务
        
        Args:
            cookies: 认证cookies
        """
        try:
            self.logger.info("🔧 使用cookies设置服务...")
            
            # 创建API服务
            api_service = self.factory.create_api_service(cookies)
            self._service_cache['api_service'] = api_service
            
            # 创建下载服务
            download_service = self.factory.create_download_service(api_service)
            self._service_cache['download_service'] = download_service
            
            self.logger.info("✅ 服务设置完成")
            
        except Exception as e:
            self.logger.error(f"❌ 设置服务失败: {e}")
            raise
    
    def clear_cache(self) -> None:
        """清空服务缓存"""
        self._service_cache.clear()
        self.logger.debug("🧹 服务缓存已清空")
    
    def get_service_status(self) -> Dict[str, bool]:
        """获取服务状态"""
        return {
            'auth_service': 'auth_service' in self._service_cache,
            'api_service': 'api_service' in self._service_cache,
            'download_service': 'download_service' in self._service_cache,
            'cache_service': 'cache_service' in self._service_cache
        }
