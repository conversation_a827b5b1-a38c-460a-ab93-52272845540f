"""
日志配置

提供日志系统的配置管理
"""

import logging
from dataclasses import dataclass
from typing import Optional, Dict, Any
from pathlib import Path


@dataclass
class LogConfig:
    """日志配置类"""
    
    # 基本配置
    level: int = logging.INFO
    format_string: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    date_format: str = "%Y-%m-%d %H:%M:%S"
    
    # 文件配置
    log_dir: Optional[str] = "logs"
    log_filename: str = "pixiv_spider.log"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    
    # 控制台配置
    console_enabled: bool = True
    console_level: int = logging.INFO
    
    # 文件配置
    file_enabled: bool = True
    file_level: int = logging.DEBUG
    
    # 编码
    encoding: str = "utf-8"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'level': self.level,
            'format_string': self.format_string,
            'date_format': self.date_format,
            'log_dir': self.log_dir,
            'log_filename': self.log_filename,
            'max_file_size': self.max_file_size,
            'backup_count': self.backup_count,
            'console_enabled': self.console_enabled,
            'console_level': self.console_level,
            'file_enabled': self.file_enabled,
            'file_level': self.file_level,
            'encoding': self.encoding
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LogConfig':
        """从字典创建配置"""
        return cls(**data)
    
    def get_log_path(self) -> Path:
        """获取日志文件路径"""
        if self.log_dir:
            return Path(self.log_dir) / self.log_filename
        return Path(self.log_filename)


# 预定义配置
DEBUG_CONFIG = LogConfig(
    level=logging.DEBUG,
    console_level=logging.DEBUG,
    file_level=logging.DEBUG
)

PRODUCTION_CONFIG = LogConfig(
    level=logging.INFO,
    console_level=logging.WARNING,
    file_level=logging.INFO
)

SILENT_CONFIG = LogConfig(
    level=logging.ERROR,
    console_enabled=False,
    file_level=logging.ERROR
)
